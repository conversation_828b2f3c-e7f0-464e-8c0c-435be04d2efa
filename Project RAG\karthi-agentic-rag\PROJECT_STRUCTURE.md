# 📁 Project Structure

This document provides a detailed overview of the <PERSON><PERSON><PERSON>'s Agentic RAG project structure.

## 🏗️ Root Directory

```
karthi-agentic-rag/
├── 📁 backend/                 # FastAPI backend application
├── 📁 frontend/                # React frontend application
├── 📁 data/                    # Data storage directory
├── 📄 .env                     # Environment variables (create from .env.example)
├── 📄 .env.example             # Environment variables template
├── 📄 .gitignore               # Git ignore rules
├── 📄 README.md                # Main documentation
├── 📄 PROJECT_STRUCTURE.md     # This file
├── 📄 setup.py                 # Automated setup script
├── 📄 start.py                 # Application startup script
├── 📄 start.bat                # Windows startup script
└── 📄 start.sh                 # Unix startup script
```

## 🔧 Backend Structure

```
backend/
├── 📁 app/                     # Main application package
│   ├── 📁 auth/                # Authentication modules
│   │   ├── 📄 __init__.py
│   │   └── 📄 auth.py          # JWT authentication logic
│   ├── 📁 upload/              # File upload and processing
│   │   ├── 📄 __init__.py
│   │   ├── 📄 handlers.py      # File upload coordination
│   │   └── 📄 processors.py    # File content extraction
│   ├── 📁 rag/                 # RAG system components
│   │   ├── 📄 __init__.py
│   │   ├── 📄 embeddings.py    # Embedding generation
│   │   ├── 📄 storage.py       # Vector storage (SQLite)
│   │   └── 📄 chat.py          # RAG chat system
│   ├── 📁 utils/               # Utility modules
│   │   ├── 📄 __init__.py
│   │   └── 📄 config.py        # Configuration management
│   ├── 📄 __init__.py
│   └── 📄 main.py              # FastAPI application
├── 📄 requirements.txt         # Python dependencies
└── 📄 run.py                   # Server startup script
```

## 🎨 Frontend Structure

```
frontend/
├── 📁 public/                  # Static files
│   └── 📄 index.html           # HTML template
├── 📁 src/                     # Source code
│   ├── 📁 components/          # Reusable components
│   │   ├── 📄 UploadArea.js    # File upload component
│   │   └── 📄 ChatInterface.js # Chat interface component
│   ├── 📁 pages/               # Page components
│   │   ├── 📄 LoginPage.js     # Login page
│   │   └── 📄 MainPage.js      # Main application page
│   ├── 📁 styles/              # CSS styles
│   │   ├── 📄 index.css        # Global styles
│   │   └── 📄 App.css          # App-specific styles
│   ├── 📁 utils/               # Utility modules
│   │   └── 📄 AuthContext.js   # Authentication context
│   ├── 📄 App.js               # Main App component
│   └── 📄 index.js             # Application entry point
└── 📄 package.json             # Node.js dependencies
```

## 💾 Data Directory

```
data/
├── 📁 uploads/                 # Uploaded files storage
│   └── 📄 .gitkeep
├── 📁 embeddings/              # Embedding cache (optional)
│   └── 📄 .gitkeep
└── 📄 knowledge_base.db        # SQLite database (created automatically)
```

## 📋 Key Files Description

### Backend Files

| File | Purpose |
|------|---------|
| `app/main.py` | FastAPI application with all endpoints |
| `app/auth/auth.py` | JWT authentication and user validation |
| `app/upload/handlers.py` | File upload coordination and processing |
| `app/upload/processors.py` | Content extraction from different file types |
| `app/rag/embeddings.py` | Text embedding using sentence-transformers |
| `app/rag/storage.py` | SQLite vector storage and similarity search |
| `app/rag/chat.py` | RAG pipeline and LLaMA-3 integration |
| `app/utils/config.py` | Configuration and environment variables |

### Frontend Files

| File | Purpose |
|------|---------|
| `src/App.js` | Main application with routing |
| `src/pages/LoginPage.js` | Authentication interface |
| `src/pages/MainPage.js` | Main application dashboard |
| `src/components/UploadArea.js` | Drag-and-drop file upload |
| `src/components/ChatInterface.js` | Chat interface with AI assistant |
| `src/utils/AuthContext.js` | Authentication state management |
| `src/styles/index.css` | Global CSS styles and theme |

### Configuration Files

| File | Purpose |
|------|---------|
| `.env` | Environment variables (user-created) |
| `.env.example` | Environment variables template |
| `backend/requirements.txt` | Python package dependencies |
| `frontend/package.json` | Node.js package dependencies |

### Utility Scripts

| File | Purpose |
|------|---------|
| `setup.py` | Automated project setup |
| `start.py` | Cross-platform application launcher |
| `start.bat` | Windows batch startup script |
| `start.sh` | Unix shell startup script |

## 🔄 Data Flow

### 1. File Upload Flow
```
User Upload → UploadArea.js → /upload API → handlers.py → processors.py → storage.py
```

### 2. Chat Flow
```
User Question → ChatInterface.js → /ask API → chat.py → embeddings.py → storage.py → LLaMA-3 API
```

### 3. Authentication Flow
```
Login Form → AuthContext.js → /login API → auth.py → JWT Token → Protected Routes
```

## 🗄️ Database Schema

### SQLite Tables

#### `chunks` table
- `id` (TEXT PRIMARY KEY) - Unique chunk identifier
- `content` (TEXT) - Text content of the chunk
- `embedding` (TEXT) - JSON serialized embedding vector
- `metadata` (TEXT) - JSON serialized metadata
- `file_id` (TEXT) - Reference to source file
- `chunk_index` (INTEGER) - Chunk position in file
- `created_at` (TIMESTAMP) - Creation timestamp
- `updated_at` (TIMESTAMP) - Last update timestamp

#### `files` table
- `id` (TEXT PRIMARY KEY) - Unique file identifier
- `filename` (TEXT) - Original filename
- `file_type` (TEXT) - File type (pdf, image, text)
- `file_size` (INTEGER) - File size in bytes
- `file_path` (TEXT) - Path to stored file
- `upload_timestamp` (TIMESTAMP) - Upload time
- `chunk_count` (INTEGER) - Number of chunks created
- `created_at` (TIMESTAMP) - Creation timestamp

## 🔧 Configuration Options

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `HF_TOKEN` | Hugging Face API token | Required |
| `SECRET_KEY` | JWT secret key | Auto-generated |
| `DEBUG` | Debug mode | True |
| `DATABASE_PATH` | SQLite database path | ./data/knowledge_base.db |
| `UPLOAD_DIR` | File upload directory | ./data/uploads |
| `MAX_FILE_SIZE` | Maximum file size | 50MB |
| `EMBEDDING_MODEL` | Sentence transformer model | all-MiniLM-L6-v2 |
| `LLM_MODEL` | Language model | Meta-Llama-3-8B-Instruct |
| `HARDCODED_USERNAME` | Login username | karthick |
| `HARDCODED_PASSWORD` | Login password | 1858 |

### RAG Configuration

| Parameter | Description | Default |
|-----------|-------------|---------|
| `CHUNK_SIZE` | Text chunk size in characters | 512 |
| `CHUNK_OVERLAP` | Overlap between chunks | 50 |
| `TOP_K_RESULTS` | Number of chunks to retrieve | 5 |
| `SIMILARITY_THRESHOLD` | Minimum similarity score | 0.7 |

## 🚀 Deployment Structure

For production deployment, consider this structure:

```
production/
├── 📁 app/                     # Application code
├── 📁 data/                    # Persistent data
├── 📁 logs/                    # Application logs
├── 📁 backups/                 # Database backups
├── 📄 docker-compose.yml       # Container orchestration
├── 📄 Dockerfile.backend       # Backend container
├── 📄 Dockerfile.frontend      # Frontend container
└── 📄 nginx.conf               # Reverse proxy config
```

## 📝 Development Guidelines

### Adding New Features

1. **Backend**: Add new endpoints in `app/main.py`
2. **Frontend**: Create components in `src/components/`
3. **Styles**: Add CSS in `src/styles/`
4. **Configuration**: Update `app/utils/config.py`

### File Processing

1. Add new processors in `app/upload/processors.py`
2. Update supported file types in `app/upload/handlers.py`
3. Update frontend validation in `components/UploadArea.js`

### RAG Improvements

1. Modify embedding logic in `app/rag/embeddings.py`
2. Update storage schema in `app/rag/storage.py`
3. Enhance chat logic in `app/rag/chat.py`
