#!/usr/bin/env python3
"""
Startup script for <PERSON><PERSON><PERSON>'s Agentic RAG application.
Starts both backend and frontend servers.
"""

import os
import sys
import subprocess
import time
import signal
import threading
from pathlib import Path

class ProcessManager:
    """Manages backend and frontend processes."""
    
    def __init__(self):
        self.backend_process = None
        self.frontend_process = None
        self.running = True
    
    def start_backend(self):
        """Start the backend server."""
        print("🚀 Starting backend server...")
        
        backend_dir = Path("backend")
        if not backend_dir.exists():
            print("❌ Backend directory not found!")
            return False
        
        # Check if virtual environment exists
        if os.name == 'nt':  # Windows
            python_path = backend_dir / "venv" / "Scripts" / "python.exe"
        else:  # Unix-like
            python_path = backend_dir / "venv" / "bin" / "python"
        
        if not python_path.exists():
            print("❌ Virtual environment not found! Run setup.py first.")
            return False
        
        try:
            self.backend_process = subprocess.Popen(
                [str(python_path), "run.py"],
                cwd=backend_dir,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True,
                bufsize=1
            )
            
            # Start thread to monitor backend output
            threading.Thread(
                target=self._monitor_process,
                args=(self.backend_process, "BACKEND"),
                daemon=True
            ).start()
            
            print("✅ Backend server started!")
            return True
            
        except Exception as e:
            print(f"❌ Failed to start backend: {e}")
            return False
    
    def start_frontend(self):
        """Start the frontend server."""
        print("🎨 Starting frontend server...")
        
        frontend_dir = Path("frontend")
        if not frontend_dir.exists():
            print("❌ Frontend directory not found!")
            return False
        
        # Check if node_modules exists
        if not (frontend_dir / "node_modules").exists():
            print("❌ Node modules not found! Run setup.py first.")
            return False
        
        try:
            # Wait a bit for backend to start
            time.sleep(3)
            
            self.frontend_process = subprocess.Popen(
                ["npm", "start"],
                cwd=frontend_dir,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True,
                bufsize=1
            )
            
            # Start thread to monitor frontend output
            threading.Thread(
                target=self._monitor_process,
                args=(self.frontend_process, "FRONTEND"),
                daemon=True
            ).start()
            
            print("✅ Frontend server started!")
            return True
            
        except Exception as e:
            print(f"❌ Failed to start frontend: {e}")
            return False
    
    def _monitor_process(self, process, name):
        """Monitor process output."""
        try:
            for line in iter(process.stdout.readline, ''):
                if self.running:
                    # Filter out some verbose logs
                    if any(skip in line.lower() for skip in ['webpack', 'compiled successfully', 'hot update']):
                        continue
                    print(f"[{name}] {line.strip()}")
                else:
                    break
        except Exception:
            pass
    
    def stop_all(self):
        """Stop all processes."""
        print("\n🛑 Stopping servers...")
        self.running = False
        
        if self.backend_process:
            try:
                self.backend_process.terminate()
                self.backend_process.wait(timeout=5)
                print("✅ Backend server stopped")
            except subprocess.TimeoutExpired:
                self.backend_process.kill()
                print("🔪 Backend server killed")
            except Exception as e:
                print(f"⚠️  Error stopping backend: {e}")
        
        if self.frontend_process:
            try:
                self.frontend_process.terminate()
                self.frontend_process.wait(timeout=5)
                print("✅ Frontend server stopped")
            except subprocess.TimeoutExpired:
                self.frontend_process.kill()
                print("🔪 Frontend server killed")
            except Exception as e:
                print(f"⚠️  Error stopping frontend: {e}")
    
    def wait_for_servers(self):
        """Wait for servers to be ready."""
        print("\n⏳ Waiting for servers to start...")
        
        # Wait for backend
        backend_ready = False
        for i in range(30):  # Wait up to 30 seconds
            try:
                import requests
                response = requests.get("http://localhost:8000/", timeout=1)
                if response.status_code == 200:
                    backend_ready = True
                    break
            except:
                pass
            time.sleep(1)
        
        if backend_ready:
            print("✅ Backend server is ready!")
        else:
            print("⚠️  Backend server may not be ready yet")
        
        # Wait a bit more for frontend
        time.sleep(5)
        print("✅ Frontend server should be ready!")

def check_environment():
    """Check if environment is properly set up."""
    env_file = Path(".env")
    if not env_file.exists():
        print("❌ .env file not found! Run setup.py first.")
        return False
    
    # Check if HF_TOKEN is set
    try:
        with open(env_file, 'r') as f:
            content = f.read()
            if 'HF_TOKEN=' not in content or 'your_hugging_face_token_here' in content:
                print("⚠️  Please set your Hugging Face token in .env file!")
                print("   Get your token from: https://huggingface.co/settings/tokens")
                return False
    except Exception as e:
        print(f"❌ Error reading .env file: {e}")
        return False
    
    return True

def print_info():
    """Print application information."""
    print("\n" + "="*60)
    print("🤖 Karthi's Agentic RAG - Application Started!")
    print("="*60)
    print("\n🌐 Access the application:")
    print("   Frontend: http://localhost:3000")
    print("   Backend API: http://localhost:8000")
    print("   API Docs: http://localhost:8000/docs")
    print("\n🔑 Login Credentials:")
    print("   Username: karthick")
    print("   Password: 1858")
    print("\n📝 Features:")
    print("   • Upload PDFs, images, and text files")
    print("   • AI-powered document analysis")
    print("   • Context-aware question answering")
    print("   • Persistent knowledge base")
    print("\n⌨️  Press Ctrl+C to stop all servers")
    print("="*60)

def main():
    """Main startup function."""
    print("🤖 Karthi's Agentic RAG Startup")
    print("="*40)
    
    # Check environment
    if not check_environment():
        print("\n💡 Run 'python setup.py' to set up the environment first.")
        return False
    
    # Create process manager
    manager = ProcessManager()
    
    # Set up signal handler for graceful shutdown
    def signal_handler(signum, frame):
        manager.stop_all()
        sys.exit(0)
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    try:
        # Start servers
        if not manager.start_backend():
            return False
        
        if not manager.start_frontend():
            manager.stop_all()
            return False
        
        # Wait for servers to be ready
        manager.wait_for_servers()
        
        # Print information
        print_info()
        
        # Keep the script running
        try:
            while manager.running:
                time.sleep(1)
        except KeyboardInterrupt:
            pass
        
        manager.stop_all()
        return True
        
    except Exception as e:
        print(f"❌ Error during startup: {e}")
        manager.stop_all()
        return False

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n👋 Goodbye!")
        sys.exit(0)
    except Exception as e:
        print(f"\n\n❌ Startup failed: {e}")
        sys.exit(1)
