# 🤖 <PERSON><PERSON><PERSON>'s Agentic RAG

A sophisticated multimodal Retrieval-Augmented Generation (RAG) system that answers questions based **exclusively** on user-uploaded content. Built with FastAPI, React, and powered by Meta-Llama-3-8B-Instruct.

## ✨ Features

- **🔐 Secure Authentication**: Simple login system with hardcoded credentials
- **📁 Multimodal Upload**: Support for PDFs, images (OCR), and text files
- **🧠 Intelligent Processing**: Automatic content extraction and chunking
- **🔍 Semantic Search**: Vector similarity search using sentence-transformers
- **💬 Smart Chat**: Context-aware responses using LLaMA-3-8B-Instruct
- **💾 Persistent Storage**: Local SQLite database for knowledge retention
- **🎨 Modern UI**: Dark theme React interface with real-time updates
- **📊 Knowledge Analytics**: Track uploaded documents and processing stats

## 🏗️ Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   React Frontend │    │  FastAPI Backend │    │ Hugging Face API │
│                 │    │                 │    │                 │
│ • Login Page    │◄──►│ • Authentication│◄──►│ • LLaMA-3-8B    │
│ • Upload UI     │    │ • File Processing│    │ • Inference     │
│ • Chat Interface│    │ • RAG Pipeline  │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │
                              ▼
                    ┌─────────────────┐
                    │ Local Storage   │
                    │                 │
                    │ • SQLite DB     │
                    │ • File Uploads  │
                    │ • Embeddings    │
                    └─────────────────┘
```

## 🚀 Quick Start

### Prerequisites

- Python 3.8+
- Node.js 16+
- Tesseract OCR (for image processing)

### 1. Clone and Setup

```bash
git clone <repository-url>
cd karthi-agentic-rag
```

### 2. Backend Setup

```bash
cd backend

# Create virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt

# Copy environment file
cp ../.env.example .env
# Edit .env with your Hugging Face token
```

### 3. Install Tesseract OCR

**Windows:**
```bash
# Download from: https://github.com/UB-Mannheim/tesseract/wiki
# Add to PATH: C:\Program Files\Tesseract-OCR
```

**macOS:**
```bash
brew install tesseract
```

**Ubuntu/Debian:**
```bash
sudo apt-get install tesseract-ocr
```

### 4. Frontend Setup

```bash
cd ../frontend

# Install dependencies
npm install

# Start development server
npm start
```

### 5. Start Backend

```bash
cd ../backend
python run.py
```

### 6. Access Application

- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:8000
- **API Docs**: http://localhost:8000/docs

## 🔑 Login Credentials

- **Username**: `karthick`
- **Password**: `1858`

## 📋 Environment Variables

Create a `.env` file in the project root:

```env
# Hugging Face API Configuration
HF_TOKEN=your_hugging_face_token_here

# Application Configuration
SECRET_KEY=your-secret-key-here-change-in-production
DEBUG=True

# Database Configuration
DATABASE_PATH=./data/knowledge_base.db

# Upload Configuration
UPLOAD_DIR=./data/uploads
MAX_FILE_SIZE=50MB

# Model Configuration
EMBEDDING_MODEL=sentence-transformers/all-MiniLM-L6-v2
LLM_MODEL=meta-llama/Meta-Llama-3-8B-Instruct

# Authentication
HARDCODED_USERNAME=karthick
HARDCODED_PASSWORD=1858
```

## 📁 Supported File Types

| Type | Extensions | Processing |
|------|------------|------------|
| **PDF** | `.pdf` | PyMuPDF text extraction |
| **Images** | `.jpg`, `.jpeg`, `.png`, `.bmp`, `.tiff`, `.gif` | Tesseract OCR |
| **Text** | `.txt`, `.md`, `.py`, `.js`, `.html`, `.css`, `.json`, `.xml`, `.csv` | Direct reading |

## 🔄 How It Works

### 1. Document Upload
- Files are uploaded via drag-and-drop interface
- Content is extracted based on file type
- Text is chunked into ~512 character segments with overlap

### 2. Embedding Generation
- Each chunk is embedded using `sentence-transformers/all-MiniLM-L6-v2`
- Embeddings are stored in SQLite with metadata

### 3. Question Answering
- User questions are embedded using the same model
- Cosine similarity search retrieves relevant chunks
- Context + question sent to LLaMA-3-8B-Instruct
- Response generated based **only** on provided context

## 🛠️ API Endpoints

### Authentication
- `POST /login` - User authentication

### File Management
- `POST /upload` - Upload and process files
- `GET /knowledge-status` - Get knowledge base statistics

### Chat
- `POST /ask` - Ask questions about uploaded content

## 🎨 UI Components

### Login Page
- Welcome screen with app branding
- Secure authentication form
- Demo login button

### Main Application
- **Sidebar**: Knowledge base stats, file types, recent uploads
- **Upload Tab**: Drag-and-drop file upload with progress
- **Chat Tab**: Real-time conversation with AI assistant

## 📊 Knowledge Base Features

- **Persistent Storage**: Knowledge retained across sessions
- **File Analytics**: Track document types and upload history
- **Chunk Management**: Automatic text segmentation and indexing
- **Similarity Search**: Efficient vector-based retrieval

## 🔧 Development

### Backend Structure
```
backend/
├── app/
│   ├── auth/          # Authentication logic
│   ├── upload/        # File processing
│   ├── rag/           # RAG pipeline
│   └── utils/         # Configuration
├── requirements.txt
└── run.py
```

### Frontend Structure
```
frontend/
├── src/
│   ├── components/    # Reusable components
│   ├── pages/         # Main pages
│   ├── styles/        # CSS styles
│   └── utils/         # Utilities
└── package.json
```

## 🚨 Important Notes

- **No Hallucination**: Responses are based **only** on uploaded content
- **Local Storage**: All data stored locally for privacy
- **Single User**: Designed for single-user authentication
- **File Limits**: 50MB per file maximum
- **Context Window**: Responses limited to retrieved chunks

## 🔍 Troubleshooting

### Common Issues

1. **Tesseract not found**
   ```bash
   # Ensure Tesseract is installed and in PATH
   tesseract --version
   ```

2. **Hugging Face API errors**
   ```bash
   # Check your HF_TOKEN in .env file
   # Ensure you have access to Meta-Llama-3-8B-Instruct
   ```

3. **Upload failures**
   ```bash
   # Check file size (max 50MB)
   # Verify file type is supported
   # Check backend logs for details
   ```

## 📈 Performance Tips

- **Chunk Size**: Adjust `CHUNK_SIZE` in config for better context
- **Similarity Threshold**: Tune `SIMILARITY_THRESHOLD` for relevance
- **Top-K Results**: Modify `TOP_K_RESULTS` for more context

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🙏 Acknowledgments

- **Meta AI** for LLaMA-3-8B-Instruct
- **Hugging Face** for model hosting and sentence-transformers
- **FastAPI** for the excellent web framework
- **React** for the frontend framework
