{"version": 3, "names": ["_helper<PERSON>lugin<PERSON><PERSON>s", "require", "_core", "_helperModuleTransforms", "_helperValidatorIdentifier", "buildTemplate", "template", "statement", "buildExportAll", "MISSING_PLUGIN_WARNING", "MISSING_PLUGIN_ERROR", "getExportSpecifierName", "node", "stringSpecifiers", "type", "name", "stringValue", "value", "isIdentifierName", "add", "Error", "constructExportCall", "path", "exportIdent", "exportNames", "exportValues", "exportStarTarget", "statements", "length", "push", "t", "expressionStatement", "callExpression", "stringLiteral", "objectProperties", "i", "exportName", "exportValue", "objectProperty", "has", "identifier", "objectExpression", "exportObj", "scope", "generateUid", "variableDeclaration", "variableDeclarator", "KEY", "generateUidIdentifier", "EXPORT_OBJ", "TARGET", "assignmentExpression", "memberExpression", "_default", "exports", "default", "declare", "api", "options", "assertVersion", "systemGlobal", "allowTopLevelThis", "reassignmentVisited", "WeakSet", "reassignmentVisitor", "AssignmentExpression|UpdateExpression", "arg", "isAssignmentExpression", "get", "isObjectPattern", "isArrayPattern", "exprs", "Object", "keys", "getBindingIdentifiers", "getBinding", "exportedNames", "exportedName", "buildCall", "expression", "replaceWith", "sequenceExpression", "isIdentifier", "isPostUpdateExpression", "isUpdateExpression", "prefix", "binaryExpression", "operator", "unaryExpression", "cloneNode", "argument", "numericLiteral", "pre", "file", "set", "visitor", "types", "importExpression", "state", "isCallExpression", "isImport", "callee", "console", "warn", "buildDynamicImport", "specifier", "contextIdent", "MetaProperty", "meta", "property", "ReferencedIdentifier", "hasBinding", "Program", "enter", "Set", "rewriteThis", "exit", "exportMap", "create", "modules", "beforeBody", "setters", "sources", "variableIds", "removedPaths", "addExportName", "key", "val", "pushModule", "source", "specifiers", "module", "for<PERSON>ach", "m", "imports", "concat", "buildExportCall", "body", "isFunctionDeclaration", "isClassDeclaration", "id", "toExpression", "isVariableDeclaration", "kind", "isImportDeclaration", "removeBinding", "remove", "isExportAllDeclaration", "isExportDefaultDeclaration", "declar", "declaration", "buildUndefinedNode", "isExportNamedDeclaration", "isFunction", "isClass", "nodes", "local", "exported", "binding", "replaceWithMultiple", "setterBody", "target", "isImportNamespaceSpecifier", "isImportDefaultSpecifier", "importSpecifier", "isImportSpecifier", "imported", "hasExportStar", "isExportSpecifier", "isStringLiteral", "functionExpression", "blockStatement", "moduleName", "getModuleName", "opts", "_path$scope", "_path$scope$hoistVari", "hoistVariables", "<PERSON><PERSON>", "prototype", "hasInit", "unshift", "map", "traverse", "hasTLA", "AwaitExpression", "stop", "Function", "skip", "noScope", "SYSTEM_REGISTER", "BEFORE_BODY", "MODULE_NAME", "SETTERS", "arrayExpression", "EXECUTE", "SOURCES", "EXPORT_IDENTIFIER", "CONTEXT_IDENTIFIER", "requeue"], "sources": ["../src/index.ts"], "sourcesContent": ["import { declare } from \"@babel/helper-plugin-utils\";\nimport { template, types as t } from \"@babel/core\";\nimport type { PluginPass, Node<PERSON><PERSON>, <PERSON>ope, Visitor } from \"@babel/core\";\nimport {\n  buildDynamicImport,\n  getModuleName,\n  rewriteThis,\n} from \"@babel/helper-module-transforms\";\nimport type { PluginOptions } from \"@babel/helper-module-transforms\";\nimport { isIdentifierName } from \"@babel/helper-validator-identifier\";\n\nconst buildTemplate = template.statement(`\n  SYSTEM_REGISTER(MODULE_NAME, SOURCES, function (EXPORT_IDENTIFIER, CONTEXT_IDENTIFIER) {\n    \"use strict\";\n    BEFORE_BODY;\n    return {\n      setters: SETTERS,\n      execute: EXECUTE,\n    };\n  });\n`);\n\nconst buildExportAll = template.statement(`\n  for (var KEY in TARGET) {\n    if (KEY !== \"default\" && KEY !== \"__esModule\") EXPORT_OBJ[KEY] = TARGET[KEY];\n  }\n`);\n\nconst MISSING_PLUGIN_WARNING = `\\\nWARNING: Dynamic import() transformation must be enabled using the\n         @babel/plugin-transform-dynamic-import plugin. Babel 8 will\n         no longer transform import() without using that plugin.\n`;\n\nconst MISSING_PLUGIN_ERROR = `\\\nERROR: Dynamic import() transformation must be enabled using the\n       @babel/plugin-transform-dynamic-import plugin. Babel 8\n       no longer transforms import() without using that plugin.\n`;\n\n//todo: use getExportSpecifierName in `helper-module-transforms` when this library is refactored to NodePath usage.\n\nexport function getExportSpecifierName(\n  node: t.Node,\n  stringSpecifiers: Set<string>,\n): string {\n  if (node.type === \"Identifier\") {\n    return node.name;\n  } else if (node.type === \"StringLiteral\") {\n    const stringValue = node.value;\n    // add specifier value to `stringSpecifiers` only when it can not be converted to an identifier name\n    // i.e In `import { \"foo\" as bar }`\n    // we do not consider `\"foo\"` to be a `stringSpecifier` because we can treat it as\n    // `import { foo as bar }`\n    // This helps minimize the size of `stringSpecifiers` and reduce overhead of checking valid identifier names\n    // when building transpiled code from metadata\n    if (!isIdentifierName(stringValue)) {\n      stringSpecifiers.add(stringValue);\n    }\n    return stringValue;\n  } else {\n    throw new Error(\n      `Expected export specifier to be either Identifier or StringLiteral, got ${node.type}`,\n    );\n  }\n}\n\ntype PluginState = {\n  contextIdent: string;\n  // List of names that should only be printed as string literals.\n  // i.e. `import { \"any unicode\" as foo } from \"some-module\"`\n  // `stringSpecifiers` is Set(1) [\"any unicode\"]\n  // In most cases `stringSpecifiers` is an empty Set\n  stringSpecifiers: Set<string>;\n};\n\ntype ModuleMetadata = {\n  key: string;\n  imports: any[];\n  exports: any[];\n};\n\nfunction constructExportCall(\n  path: NodePath<t.Program>,\n  exportIdent: t.Identifier,\n  exportNames: string[],\n  exportValues: t.Expression[],\n  exportStarTarget: t.Identifier | null,\n  stringSpecifiers: Set<string>,\n) {\n  const statements = [];\n  if (!exportStarTarget) {\n    if (exportNames.length === 1) {\n      statements.push(\n        t.expressionStatement(\n          t.callExpression(exportIdent, [\n            t.stringLiteral(exportNames[0]),\n            exportValues[0],\n          ]),\n        ),\n      );\n    } else {\n      const objectProperties = [];\n      for (let i = 0; i < exportNames.length; i++) {\n        const exportName = exportNames[i];\n        const exportValue = exportValues[i];\n        objectProperties.push(\n          t.objectProperty(\n            stringSpecifiers.has(exportName)\n              ? t.stringLiteral(exportName)\n              : t.identifier(exportName),\n            exportValue,\n          ),\n        );\n      }\n      statements.push(\n        t.expressionStatement(\n          t.callExpression(exportIdent, [t.objectExpression(objectProperties)]),\n        ),\n      );\n    }\n  } else {\n    const exportObj = path.scope.generateUid(\"exportObj\");\n\n    statements.push(\n      t.variableDeclaration(\"var\", [\n        t.variableDeclarator(t.identifier(exportObj), t.objectExpression([])),\n      ]),\n    );\n\n    statements.push(\n      buildExportAll({\n        KEY: path.scope.generateUidIdentifier(\"key\"),\n        EXPORT_OBJ: t.identifier(exportObj),\n        TARGET: exportStarTarget,\n      }),\n    );\n\n    for (let i = 0; i < exportNames.length; i++) {\n      const exportName = exportNames[i];\n      const exportValue = exportValues[i];\n\n      statements.push(\n        t.expressionStatement(\n          t.assignmentExpression(\n            \"=\",\n            t.memberExpression(\n              t.identifier(exportObj),\n              t.identifier(exportName),\n            ),\n            exportValue,\n          ),\n        ),\n      );\n    }\n\n    statements.push(\n      t.expressionStatement(\n        t.callExpression(exportIdent, [t.identifier(exportObj)]),\n      ),\n    );\n  }\n  return statements;\n}\n\nexport interface Options extends PluginOptions {\n  allowTopLevelThis?: boolean;\n  systemGlobal?: string;\n}\n\ntype ReassignmentVisitorState = {\n  scope: Scope;\n  exports: any;\n  buildCall: (name: string, value: t.Expression) => t.ExpressionStatement;\n};\n\nexport default declare<PluginState>((api, options: Options) => {\n  api.assertVersion(REQUIRED_VERSION(7));\n\n  const { systemGlobal = \"System\", allowTopLevelThis = false } = options;\n  const reassignmentVisited = new WeakSet();\n\n  const reassignmentVisitor: Visitor<ReassignmentVisitorState> = {\n    \"AssignmentExpression|UpdateExpression\"(\n      path: NodePath<t.AssignmentExpression | t.UpdateExpression>,\n    ) {\n      if (reassignmentVisited.has(path.node)) return;\n      reassignmentVisited.add(path.node);\n\n      const arg = path.isAssignmentExpression()\n        ? path.get(\"left\")\n        : path.get(\"argument\");\n\n      if (arg.isObjectPattern() || arg.isArrayPattern()) {\n        const exprs: t.SequenceExpression[\"expressions\"] = [path.node];\n        for (const name of Object.keys(arg.getBindingIdentifiers())) {\n          if (this.scope.getBinding(name) !== path.scope.getBinding(name)) {\n            return;\n          }\n          const exportedNames = this.exports[name];\n          if (!exportedNames) continue;\n          for (const exportedName of exportedNames) {\n            exprs.push(\n              this.buildCall(exportedName, t.identifier(name)).expression,\n            );\n          }\n        }\n        path.replaceWith(t.sequenceExpression(exprs));\n        return;\n      }\n\n      if (!arg.isIdentifier()) return;\n\n      const name = arg.node.name;\n\n      // redeclared in this scope\n      if (this.scope.getBinding(name) !== path.scope.getBinding(name)) return;\n\n      const exportedNames = this.exports[name];\n      if (!exportedNames) return;\n\n      let node: t.Expression = path.node;\n\n      // if it is a non-prefix update expression (x++ etc)\n      // then we must replace with the expression (_export('x', x + 1), x++)\n      // in order to ensure the same update expression value\n      const isPostUpdateExpression = t.isUpdateExpression(node, {\n        prefix: false,\n      });\n      if (isPostUpdateExpression) {\n        node = t.binaryExpression(\n          // @ts-expect-error The operator of a post-update expression must be \"++\" | \"--\"\n          node.operator[0],\n          t.unaryExpression(\n            \"+\",\n            t.cloneNode(\n              // @ts-expect-error node is UpdateExpression\n              node.argument,\n            ),\n          ),\n          t.numericLiteral(1),\n        );\n      }\n\n      for (const exportedName of exportedNames) {\n        node = this.buildCall(exportedName, node).expression;\n      }\n\n      if (isPostUpdateExpression) {\n        node = t.sequenceExpression([node, path.node]);\n      }\n\n      path.replaceWith(node);\n    },\n  };\n\n  return {\n    name: \"transform-modules-systemjs\",\n\n    pre() {\n      this.file.set(\"@babel/plugin-transform-modules-*\", \"systemjs\");\n    },\n\n    visitor: {\n      [\"CallExpression\" +\n        (api.types.importExpression ? \"|ImportExpression\" : \"\")](\n        this: PluginPass & PluginState,\n        path: NodePath<t.CallExpression | t.ImportExpression>,\n        state: PluginState,\n      ) {\n        if (path.isCallExpression() && !t.isImport(path.node.callee)) return;\n        if (path.isCallExpression()) {\n          if (!this.file.has(\"@babel/plugin-proposal-dynamic-import\")) {\n            if (process.env.BABEL_8_BREAKING) {\n              throw new Error(MISSING_PLUGIN_ERROR);\n            } else {\n              console.warn(MISSING_PLUGIN_WARNING);\n            }\n          }\n        } else {\n          // when createImportExpressions is true, we require the dynamic import transform\n          if (!this.file.has(\"@babel/plugin-proposal-dynamic-import\")) {\n            throw new Error(MISSING_PLUGIN_ERROR);\n          }\n        }\n        path.replaceWith(\n          buildDynamicImport(path.node, false, true, specifier =>\n            t.callExpression(\n              t.memberExpression(\n                t.identifier(state.contextIdent),\n                t.identifier(\"import\"),\n              ),\n              [specifier],\n            ),\n          ),\n        );\n      },\n\n      MetaProperty(path, state: PluginState) {\n        if (\n          path.node.meta.name === \"import\" &&\n          path.node.property.name === \"meta\"\n        ) {\n          path.replaceWith(\n            t.memberExpression(\n              t.identifier(state.contextIdent),\n              t.identifier(\"meta\"),\n            ),\n          );\n        }\n      },\n\n      ReferencedIdentifier(path, state) {\n        if (\n          path.node.name === \"__moduleName\" &&\n          !path.scope.hasBinding(\"__moduleName\")\n        ) {\n          path.replaceWith(\n            t.memberExpression(\n              t.identifier(state.contextIdent),\n              t.identifier(\"id\"),\n            ),\n          );\n        }\n      },\n\n      Program: {\n        enter(path, state) {\n          state.contextIdent = path.scope.generateUid(\"context\");\n          state.stringSpecifiers = new Set();\n          if (!allowTopLevelThis) {\n            rewriteThis(path);\n          }\n        },\n        exit(path, state) {\n          const scope = path.scope;\n          const exportIdent = scope.generateUid(\"export\");\n          const { contextIdent, stringSpecifiers } = state;\n\n          const exportMap: Record<string, string[]> = Object.create(null);\n          const modules: ModuleMetadata[] = [];\n\n          const beforeBody = [];\n          const setters: t.Expression[] = [];\n          const sources: t.StringLiteral[] = [];\n          const variableIds = [];\n          const removedPaths = [];\n\n          function addExportName(key: string, val: string) {\n            exportMap[key] = exportMap[key] || [];\n            exportMap[key].push(val);\n          }\n\n          function pushModule(\n            source: string,\n            key: \"imports\" | \"exports\",\n            specifiers: t.ModuleSpecifier[] | t.ExportAllDeclaration,\n          ) {\n            let module: ModuleMetadata;\n            modules.forEach(function (m) {\n              if (m.key === source) {\n                module = m;\n              }\n            });\n            if (!module) {\n              modules.push(\n                (module = { key: source, imports: [], exports: [] }),\n              );\n            }\n            module[key] = module[key].concat(specifiers);\n          }\n\n          function buildExportCall(name: string, val: t.Expression) {\n            return t.expressionStatement(\n              t.callExpression(t.identifier(exportIdent), [\n                t.stringLiteral(name),\n                val,\n              ]),\n            );\n          }\n\n          const exportNames = [];\n          const exportValues: t.Expression[] = [];\n\n          const body = path.get(\"body\");\n\n          for (const path of body) {\n            if (path.isFunctionDeclaration()) {\n              beforeBody.push(path.node);\n              removedPaths.push(path);\n            } else if (path.isClassDeclaration()) {\n              variableIds.push(t.cloneNode(path.node.id));\n              path.replaceWith(\n                t.expressionStatement(\n                  t.assignmentExpression(\n                    \"=\",\n                    t.cloneNode(path.node.id),\n                    t.toExpression(path.node),\n                  ),\n                ),\n              );\n            } else if (path.isVariableDeclaration()) {\n              // Convert top-level variable declarations to \"var\",\n              // because they must be hoisted\n              path.node.kind = \"var\";\n            } else if (path.isImportDeclaration()) {\n              const source = path.node.source.value;\n              pushModule(source, \"imports\", path.node.specifiers);\n              for (const name of Object.keys(path.getBindingIdentifiers())) {\n                scope.removeBinding(name);\n                variableIds.push(t.identifier(name));\n              }\n              path.remove();\n            } else if (path.isExportAllDeclaration()) {\n              pushModule(path.node.source.value, \"exports\", path.node);\n              path.remove();\n            } else if (path.isExportDefaultDeclaration()) {\n              const declar = path.node.declaration;\n              if (t.isClassDeclaration(declar)) {\n                const id = declar.id;\n                if (id) {\n                  exportNames.push(\"default\");\n                  exportValues.push(scope.buildUndefinedNode());\n                  variableIds.push(t.cloneNode(id));\n                  addExportName(id.name, \"default\");\n                  path.replaceWith(\n                    t.expressionStatement(\n                      t.assignmentExpression(\n                        \"=\",\n                        t.cloneNode(id),\n                        t.toExpression(declar),\n                      ),\n                    ),\n                  );\n                } else {\n                  exportNames.push(\"default\");\n                  exportValues.push(t.toExpression(declar));\n                  removedPaths.push(path);\n                }\n              } else if (t.isFunctionDeclaration(declar)) {\n                const id = declar.id;\n                if (id) {\n                  beforeBody.push(declar);\n                  exportNames.push(\"default\");\n                  exportValues.push(t.cloneNode(id));\n                  addExportName(id.name, \"default\");\n                } else {\n                  exportNames.push(\"default\");\n                  exportValues.push(t.toExpression(declar));\n                }\n                removedPaths.push(path);\n              } else {\n                // @ts-expect-error TSDeclareFunction is not expected here\n                path.replaceWith(buildExportCall(\"default\", declar));\n              }\n            } else if (path.isExportNamedDeclaration()) {\n              const declar = path.node.declaration;\n\n              if (declar) {\n                path.replaceWith(declar);\n\n                if (t.isFunction(declar)) {\n                  const name = declar.id.name;\n                  addExportName(name, name);\n                  beforeBody.push(declar);\n                  exportNames.push(name);\n                  exportValues.push(t.cloneNode(declar.id));\n                  removedPaths.push(path);\n                } else if (t.isClass(declar)) {\n                  const name = declar.id.name;\n                  exportNames.push(name);\n                  exportValues.push(scope.buildUndefinedNode());\n                  variableIds.push(t.cloneNode(declar.id));\n                  path.replaceWith(\n                    t.expressionStatement(\n                      t.assignmentExpression(\n                        \"=\",\n                        t.cloneNode(declar.id),\n                        t.toExpression(declar),\n                      ),\n                    ),\n                  );\n                  addExportName(name, name);\n                } else {\n                  if (t.isVariableDeclaration(declar)) {\n                    // Convert top-level variable declarations to \"var\",\n                    // because they must be hoisted\n                    declar.kind = \"var\";\n                  }\n                  for (const name of Object.keys(\n                    t.getBindingIdentifiers(declar),\n                  )) {\n                    addExportName(name, name);\n                  }\n                }\n              } else {\n                const specifiers = path.node.specifiers;\n                if (specifiers?.length) {\n                  if (path.node.source) {\n                    pushModule(path.node.source.value, \"exports\", specifiers);\n                    path.remove();\n                  } else {\n                    const nodes = [];\n\n                    for (const specifier of specifiers) {\n                      // @ts-expect-error This isn't an \"export ... from\" declaration\n                      // because path.node.source is falsy, so the local specifier exists.\n                      const { local, exported } = specifier;\n\n                      const binding = scope.getBinding(local.name);\n                      const exportedName = getExportSpecifierName(\n                        exported,\n                        stringSpecifiers,\n                      );\n                      // hoisted function export\n                      if (\n                        binding &&\n                        t.isFunctionDeclaration(binding.path.node)\n                      ) {\n                        exportNames.push(exportedName);\n                        exportValues.push(t.cloneNode(local));\n                      }\n                      // only globals also exported this way\n                      else if (!binding) {\n                        nodes.push(buildExportCall(exportedName, local));\n                      }\n                      addExportName(local.name, exportedName);\n                    }\n\n                    path.replaceWithMultiple(nodes);\n                  }\n                } else {\n                  path.remove();\n                }\n              }\n            }\n          }\n\n          modules.forEach(function (specifiers) {\n            const setterBody = [];\n            const target = scope.generateUid(specifiers.key);\n\n            for (let specifier of specifiers.imports) {\n              if (t.isImportNamespaceSpecifier(specifier)) {\n                setterBody.push(\n                  t.expressionStatement(\n                    t.assignmentExpression(\n                      \"=\",\n                      specifier.local,\n                      t.identifier(target),\n                    ),\n                  ),\n                );\n              } else if (t.isImportDefaultSpecifier(specifier)) {\n                specifier = t.importSpecifier(\n                  specifier.local,\n                  t.identifier(\"default\"),\n                );\n              }\n\n              if (t.isImportSpecifier(specifier)) {\n                const { imported } = specifier;\n                setterBody.push(\n                  t.expressionStatement(\n                    t.assignmentExpression(\n                      \"=\",\n                      specifier.local,\n                      t.memberExpression(\n                        t.identifier(target),\n                        specifier.imported,\n                        /* computed */ imported.type === \"StringLiteral\",\n                      ),\n                    ),\n                  ),\n                );\n              }\n            }\n\n            if (specifiers.exports.length) {\n              const exportNames = [];\n              const exportValues = [];\n              let hasExportStar = false;\n\n              for (const node of specifiers.exports) {\n                if (t.isExportAllDeclaration(node)) {\n                  hasExportStar = true;\n                } else if (t.isExportSpecifier(node)) {\n                  const exportedName = getExportSpecifierName(\n                    node.exported,\n                    stringSpecifiers,\n                  );\n                  exportNames.push(exportedName);\n                  exportValues.push(\n                    t.memberExpression(\n                      t.identifier(target),\n                      node.local,\n                      t.isStringLiteral(node.local),\n                    ),\n                  );\n                } else {\n                  // todo\n                }\n              }\n\n              setterBody.push(\n                ...constructExportCall(\n                  path,\n                  t.identifier(exportIdent),\n                  exportNames,\n                  exportValues,\n                  hasExportStar ? t.identifier(target) : null,\n                  stringSpecifiers,\n                ),\n              );\n            }\n\n            sources.push(t.stringLiteral(specifiers.key));\n            setters.push(\n              t.functionExpression(\n                null,\n                [t.identifier(target)],\n                t.blockStatement(setterBody),\n              ),\n            );\n          });\n\n          let moduleName = getModuleName(this.file.opts, options);\n          // @ts-expect-error todo(flow->ts): do not reuse variables\n          if (moduleName) moduleName = t.stringLiteral(moduleName);\n\n          if (!process.env.BABEL_8_BREAKING && !USE_ESM && !IS_STANDALONE) {\n            // polyfill when being run by an older Babel version\n            path.scope.hoistVariables ??=\n              // eslint-disable-next-line no-restricted-globals\n              require(\"@babel/traverse\").Scope.prototype.hoistVariables;\n          }\n\n          path.scope.hoistVariables((id, hasInit) => {\n            variableIds.push(id);\n            if (!hasInit && id.name in exportMap) {\n              for (const exported of exportMap[id.name]) {\n                exportNames.push(exported);\n                exportValues.push(t.buildUndefinedNode());\n              }\n            }\n          });\n\n          if (variableIds.length) {\n            beforeBody.unshift(\n              t.variableDeclaration(\n                \"var\",\n                variableIds.map(id => t.variableDeclarator(id)),\n              ),\n            );\n          }\n\n          if (exportNames.length) {\n            beforeBody.push(\n              ...constructExportCall(\n                path,\n                t.identifier(exportIdent),\n                exportNames,\n                exportValues,\n                null,\n                stringSpecifiers,\n              ),\n            );\n          }\n\n          path.traverse(reassignmentVisitor, {\n            exports: exportMap,\n            buildCall: buildExportCall,\n            scope,\n          });\n\n          for (const path of removedPaths) {\n            path.remove();\n          }\n\n          let hasTLA = false;\n          path.traverse({\n            AwaitExpression(path) {\n              hasTLA = true;\n              path.stop();\n            },\n            Function(path) {\n              path.skip();\n            },\n            // @ts-expect-error - todo: add noScope to type definitions\n            noScope: true,\n          });\n\n          path.node.body = [\n            buildTemplate({\n              SYSTEM_REGISTER: t.memberExpression(\n                t.identifier(systemGlobal),\n                t.identifier(\"register\"),\n              ),\n              BEFORE_BODY: beforeBody,\n              MODULE_NAME: moduleName,\n              SETTERS: t.arrayExpression(setters),\n              EXECUTE: t.functionExpression(\n                null,\n                [],\n                t.blockStatement(path.node.body),\n                false,\n                hasTLA,\n              ),\n              SOURCES: t.arrayExpression(sources),\n              EXPORT_IDENTIFIER: t.identifier(exportIdent),\n              CONTEXT_IDENTIFIER: t.identifier(contextIdent),\n            }),\n          ];\n          path.requeue(path.get(\"body.0\"));\n        },\n      },\n    },\n  };\n});\n"], "mappings": ";;;;;;;AAAA,IAAAA,kBAAA,GAAAC,OAAA;AACA,IAAAC,KAAA,GAAAD,OAAA;AAEA,IAAAE,uBAAA,GAAAF,OAAA;AAMA,IAAAG,0BAAA,GAAAH,OAAA;AAEA,MAAMI,aAAa,GAAGC,cAAQ,CAACC,SAAS,CAAC;AACzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,CAAC;AAEF,MAAMC,cAAc,GAAGF,cAAQ,CAACC,SAAS,CAAC;AAC1C;AACA;AACA;AACA,CAAC,CAAC;AAEF,MAAME,sBAAsB,GAAG;AAC/B;AACA;AACA;AACA,CAAC;AAED,MAAMC,oBAAoB,GAAG;AAC7B;AACA;AACA;AACA,CAAC;AAIM,SAASC,sBAAsBA,CACpCC,IAAY,EACZC,gBAA6B,EACrB;EACR,IAAID,IAAI,CAACE,IAAI,KAAK,YAAY,EAAE;IAC9B,OAAOF,IAAI,CAACG,IAAI;EAClB,CAAC,MAAM,IAAIH,IAAI,CAACE,IAAI,KAAK,eAAe,EAAE;IACxC,MAAME,WAAW,GAAGJ,IAAI,CAACK,KAAK;IAO9B,IAAI,CAAC,IAAAC,2CAAgB,EAACF,WAAW,CAAC,EAAE;MAClCH,gBAAgB,CAACM,GAAG,CAACH,WAAW,CAAC;IACnC;IACA,OAAOA,WAAW;EACpB,CAAC,MAAM;IACL,MAAM,IAAII,KAAK,CACb,2EAA2ER,IAAI,CAACE,IAAI,EACtF,CAAC;EACH;AACF;AAiBA,SAASO,mBAAmBA,CAC1BC,IAAyB,EACzBC,WAAyB,EACzBC,WAAqB,EACrBC,YAA4B,EAC5BC,gBAAqC,EACrCb,gBAA6B,EAC7B;EACA,MAAMc,UAAU,GAAG,EAAE;EACrB,IAAI,CAACD,gBAAgB,EAAE;IACrB,IAAIF,WAAW,CAACI,MAAM,KAAK,CAAC,EAAE;MAC5BD,UAAU,CAACE,IAAI,CACbC,WAAC,CAACC,mBAAmB,CACnBD,WAAC,CAACE,cAAc,CAACT,WAAW,EAAE,CAC5BO,WAAC,CAACG,aAAa,CAACT,WAAW,CAAC,CAAC,CAAC,CAAC,EAC/BC,YAAY,CAAC,CAAC,CAAC,CAChB,CACH,CACF,CAAC;IACH,CAAC,MAAM;MACL,MAAMS,gBAAgB,GAAG,EAAE;MAC3B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGX,WAAW,CAACI,MAAM,EAAEO,CAAC,EAAE,EAAE;QAC3C,MAAMC,UAAU,GAAGZ,WAAW,CAACW,CAAC,CAAC;QACjC,MAAME,WAAW,GAAGZ,YAAY,CAACU,CAAC,CAAC;QACnCD,gBAAgB,CAACL,IAAI,CACnBC,WAAC,CAACQ,cAAc,CACdzB,gBAAgB,CAAC0B,GAAG,CAACH,UAAU,CAAC,GAC5BN,WAAC,CAACG,aAAa,CAACG,UAAU,CAAC,GAC3BN,WAAC,CAACU,UAAU,CAACJ,UAAU,CAAC,EAC5BC,WACF,CACF,CAAC;MACH;MACAV,UAAU,CAACE,IAAI,CACbC,WAAC,CAACC,mBAAmB,CACnBD,WAAC,CAACE,cAAc,CAACT,WAAW,EAAE,CAACO,WAAC,CAACW,gBAAgB,CAACP,gBAAgB,CAAC,CAAC,CACtE,CACF,CAAC;IACH;EACF,CAAC,MAAM;IACL,MAAMQ,SAAS,GAAGpB,IAAI,CAACqB,KAAK,CAACC,WAAW,CAAC,WAAW,CAAC;IAErDjB,UAAU,CAACE,IAAI,CACbC,WAAC,CAACe,mBAAmB,CAAC,KAAK,EAAE,CAC3Bf,WAAC,CAACgB,kBAAkB,CAAChB,WAAC,CAACU,UAAU,CAACE,SAAS,CAAC,EAAEZ,WAAC,CAACW,gBAAgB,CAAC,EAAE,CAAC,CAAC,CACtE,CACH,CAAC;IAEDd,UAAU,CAACE,IAAI,CACbrB,cAAc,CAAC;MACbuC,GAAG,EAAEzB,IAAI,CAACqB,KAAK,CAACK,qBAAqB,CAAC,KAAK,CAAC;MAC5CC,UAAU,EAAEnB,WAAC,CAACU,UAAU,CAACE,SAAS,CAAC;MACnCQ,MAAM,EAAExB;IACV,CAAC,CACH,CAAC;IAED,KAAK,IAAIS,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGX,WAAW,CAACI,MAAM,EAAEO,CAAC,EAAE,EAAE;MAC3C,MAAMC,UAAU,GAAGZ,WAAW,CAACW,CAAC,CAAC;MACjC,MAAME,WAAW,GAAGZ,YAAY,CAACU,CAAC,CAAC;MAEnCR,UAAU,CAACE,IAAI,CACbC,WAAC,CAACC,mBAAmB,CACnBD,WAAC,CAACqB,oBAAoB,CACpB,GAAG,EACHrB,WAAC,CAACsB,gBAAgB,CAChBtB,WAAC,CAACU,UAAU,CAACE,SAAS,CAAC,EACvBZ,WAAC,CAACU,UAAU,CAACJ,UAAU,CACzB,CAAC,EACDC,WACF,CACF,CACF,CAAC;IACH;IAEAV,UAAU,CAACE,IAAI,CACbC,WAAC,CAACC,mBAAmB,CACnBD,WAAC,CAACE,cAAc,CAACT,WAAW,EAAE,CAACO,WAAC,CAACU,UAAU,CAACE,SAAS,CAAC,CAAC,CACzD,CACF,CAAC;EACH;EACA,OAAOf,UAAU;AACnB;AAAC,IAAA0B,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAac,IAAAC,0BAAO,EAAc,CAACC,GAAG,EAAEC,OAAgB,KAAK;EAC7DD,GAAG,CAACE,aAAa,CAAkB,CAAE,CAAC;EAEtC,MAAM;IAAEC,YAAY,GAAG,QAAQ;IAAEC,iBAAiB,GAAG;EAAM,CAAC,GAAGH,OAAO;EACtE,MAAMI,mBAAmB,GAAG,IAAIC,OAAO,CAAC,CAAC;EAEzC,MAAMC,mBAAsD,GAAG;IAC7D,uCAAuCC,CACrC3C,IAA2D,EAC3D;MACA,IAAIwC,mBAAmB,CAACvB,GAAG,CAACjB,IAAI,CAACV,IAAI,CAAC,EAAE;MACxCkD,mBAAmB,CAAC3C,GAAG,CAACG,IAAI,CAACV,IAAI,CAAC;MAElC,MAAMsD,GAAG,GAAG5C,IAAI,CAAC6C,sBAAsB,CAAC,CAAC,GACrC7C,IAAI,CAAC8C,GAAG,CAAC,MAAM,CAAC,GAChB9C,IAAI,CAAC8C,GAAG,CAAC,UAAU,CAAC;MAExB,IAAIF,GAAG,CAACG,eAAe,CAAC,CAAC,IAAIH,GAAG,CAACI,cAAc,CAAC,CAAC,EAAE;QACjD,MAAMC,KAA0C,GAAG,CAACjD,IAAI,CAACV,IAAI,CAAC;QAC9D,KAAK,MAAMG,IAAI,IAAIyD,MAAM,CAACC,IAAI,CAACP,GAAG,CAACQ,qBAAqB,CAAC,CAAC,CAAC,EAAE;UAC3D,IAAI,IAAI,CAAC/B,KAAK,CAACgC,UAAU,CAAC5D,IAAI,CAAC,KAAKO,IAAI,CAACqB,KAAK,CAACgC,UAAU,CAAC5D,IAAI,CAAC,EAAE;YAC/D;UACF;UACA,MAAM6D,aAAa,GAAG,IAAI,CAACtB,OAAO,CAACvC,IAAI,CAAC;UACxC,IAAI,CAAC6D,aAAa,EAAE;UACpB,KAAK,MAAMC,YAAY,IAAID,aAAa,EAAE;YACxCL,KAAK,CAAC1C,IAAI,CACR,IAAI,CAACiD,SAAS,CAACD,YAAY,EAAE/C,WAAC,CAACU,UAAU,CAACzB,IAAI,CAAC,CAAC,CAACgE,UACnD,CAAC;UACH;QACF;QACAzD,IAAI,CAAC0D,WAAW,CAAClD,WAAC,CAACmD,kBAAkB,CAACV,KAAK,CAAC,CAAC;QAC7C;MACF;MAEA,IAAI,CAACL,GAAG,CAACgB,YAAY,CAAC,CAAC,EAAE;MAEzB,MAAMnE,IAAI,GAAGmD,GAAG,CAACtD,IAAI,CAACG,IAAI;MAG1B,IAAI,IAAI,CAAC4B,KAAK,CAACgC,UAAU,CAAC5D,IAAI,CAAC,KAAKO,IAAI,CAACqB,KAAK,CAACgC,UAAU,CAAC5D,IAAI,CAAC,EAAE;MAEjE,MAAM6D,aAAa,GAAG,IAAI,CAACtB,OAAO,CAACvC,IAAI,CAAC;MACxC,IAAI,CAAC6D,aAAa,EAAE;MAEpB,IAAIhE,IAAkB,GAAGU,IAAI,CAACV,IAAI;MAKlC,MAAMuE,sBAAsB,GAAGrD,WAAC,CAACsD,kBAAkB,CAACxE,IAAI,EAAE;QACxDyE,MAAM,EAAE;MACV,CAAC,CAAC;MACF,IAAIF,sBAAsB,EAAE;QAC1BvE,IAAI,GAAGkB,WAAC,CAACwD,gBAAgB,CAEvB1E,IAAI,CAAC2E,QAAQ,CAAC,CAAC,CAAC,EAChBzD,WAAC,CAAC0D,eAAe,CACf,GAAG,EACH1D,WAAC,CAAC2D,SAAS,CAET7E,IAAI,CAAC8E,QACP,CACF,CAAC,EACD5D,WAAC,CAAC6D,cAAc,CAAC,CAAC,CACpB,CAAC;MACH;MAEA,KAAK,MAAMd,YAAY,IAAID,aAAa,EAAE;QACxChE,IAAI,GAAG,IAAI,CAACkE,SAAS,CAACD,YAAY,EAAEjE,IAAI,CAAC,CAACmE,UAAU;MACtD;MAEA,IAAII,sBAAsB,EAAE;QAC1BvE,IAAI,GAAGkB,WAAC,CAACmD,kBAAkB,CAAC,CAACrE,IAAI,EAAEU,IAAI,CAACV,IAAI,CAAC,CAAC;MAChD;MAEAU,IAAI,CAAC0D,WAAW,CAACpE,IAAI,CAAC;IACxB;EACF,CAAC;EAED,OAAO;IACLG,IAAI,EAAE,4BAA4B;IAElC6E,GAAGA,CAAA,EAAG;MACJ,IAAI,CAACC,IAAI,CAACC,GAAG,CAAC,mCAAmC,EAAE,UAAU,CAAC;IAChE,CAAC;IAEDC,OAAO,EAAE;MACP,CAAC,gBAAgB,IACdtC,GAAG,CAACuC,KAAK,CAACC,gBAAgB,GAAG,mBAAmB,GAAG,EAAE,CAAC,EAEvD3E,IAAqD,EACrD4E,KAAkB,EAClB;QACA,IAAI5E,IAAI,CAAC6E,gBAAgB,CAAC,CAAC,IAAI,CAACrE,WAAC,CAACsE,QAAQ,CAAC9E,IAAI,CAACV,IAAI,CAACyF,MAAM,CAAC,EAAE;QAC9D,IAAI/E,IAAI,CAAC6E,gBAAgB,CAAC,CAAC,EAAE;UAC3B,IAAI,CAAC,IAAI,CAACN,IAAI,CAACtD,GAAG,CAAC,uCAAuC,CAAC,EAAE;YAGpD;cACL+D,OAAO,CAACC,IAAI,CAAC9F,sBAAsB,CAAC;YACtC;UACF;QACF,CAAC,MAAM;UAEL,IAAI,CAAC,IAAI,CAACoF,IAAI,CAACtD,GAAG,CAAC,uCAAuC,CAAC,EAAE;YAC3D,MAAM,IAAInB,KAAK,CAACV,oBAAoB,CAAC;UACvC;QACF;QACAY,IAAI,CAAC0D,WAAW,CACd,IAAAwB,0CAAkB,EAAClF,IAAI,CAACV,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE6F,SAAS,IAClD3E,WAAC,CAACE,cAAc,CACdF,WAAC,CAACsB,gBAAgB,CAChBtB,WAAC,CAACU,UAAU,CAAC0D,KAAK,CAACQ,YAAY,CAAC,EAChC5E,WAAC,CAACU,UAAU,CAAC,QAAQ,CACvB,CAAC,EACD,CAACiE,SAAS,CACZ,CACF,CACF,CAAC;MACH,CAAC;MAEDE,YAAYA,CAACrF,IAAI,EAAE4E,KAAkB,EAAE;QACrC,IACE5E,IAAI,CAACV,IAAI,CAACgG,IAAI,CAAC7F,IAAI,KAAK,QAAQ,IAChCO,IAAI,CAACV,IAAI,CAACiG,QAAQ,CAAC9F,IAAI,KAAK,MAAM,EAClC;UACAO,IAAI,CAAC0D,WAAW,CACdlD,WAAC,CAACsB,gBAAgB,CAChBtB,WAAC,CAACU,UAAU,CAAC0D,KAAK,CAACQ,YAAY,CAAC,EAChC5E,WAAC,CAACU,UAAU,CAAC,MAAM,CACrB,CACF,CAAC;QACH;MACF,CAAC;MAEDsE,oBAAoBA,CAACxF,IAAI,EAAE4E,KAAK,EAAE;QAChC,IACE5E,IAAI,CAACV,IAAI,CAACG,IAAI,KAAK,cAAc,IACjC,CAACO,IAAI,CAACqB,KAAK,CAACoE,UAAU,CAAC,cAAc,CAAC,EACtC;UACAzF,IAAI,CAAC0D,WAAW,CACdlD,WAAC,CAACsB,gBAAgB,CAChBtB,WAAC,CAACU,UAAU,CAAC0D,KAAK,CAACQ,YAAY,CAAC,EAChC5E,WAAC,CAACU,UAAU,CAAC,IAAI,CACnB,CACF,CAAC;QACH;MACF,CAAC;MAEDwE,OAAO,EAAE;QACPC,KAAKA,CAAC3F,IAAI,EAAE4E,KAAK,EAAE;UACjBA,KAAK,CAACQ,YAAY,GAAGpF,IAAI,CAACqB,KAAK,CAACC,WAAW,CAAC,SAAS,CAAC;UACtDsD,KAAK,CAACrF,gBAAgB,GAAG,IAAIqG,GAAG,CAAC,CAAC;UAClC,IAAI,CAACrD,iBAAiB,EAAE;YACtB,IAAAsD,mCAAW,EAAC7F,IAAI,CAAC;UACnB;QACF,CAAC;QACD8F,IAAIA,CAAC9F,IAAI,EAAE4E,KAAK,EAAE;UAChB,MAAMvD,KAAK,GAAGrB,IAAI,CAACqB,KAAK;UACxB,MAAMpB,WAAW,GAAGoB,KAAK,CAACC,WAAW,CAAC,QAAQ,CAAC;UAC/C,MAAM;YAAE8D,YAAY;YAAE7F;UAAiB,CAAC,GAAGqF,KAAK;UAEhD,MAAMmB,SAAmC,GAAG7C,MAAM,CAAC8C,MAAM,CAAC,IAAI,CAAC;UAC/D,MAAMC,OAAyB,GAAG,EAAE;UAEpC,MAAMC,UAAU,GAAG,EAAE;UACrB,MAAMC,OAAuB,GAAG,EAAE;UAClC,MAAMC,OAA0B,GAAG,EAAE;UACrC,MAAMC,WAAW,GAAG,EAAE;UACtB,MAAMC,YAAY,GAAG,EAAE;UAEvB,SAASC,aAAaA,CAACC,GAAW,EAAEC,GAAW,EAAE;YAC/CV,SAAS,CAACS,GAAG,CAAC,GAAGT,SAAS,CAACS,GAAG,CAAC,IAAI,EAAE;YACrCT,SAAS,CAACS,GAAG,CAAC,CAACjG,IAAI,CAACkG,GAAG,CAAC;UAC1B;UAEA,SAASC,UAAUA,CACjBC,MAAc,EACdH,GAA0B,EAC1BI,UAAwD,EACxD;YACA,IAAIC,MAAsB;YAC1BZ,OAAO,CAACa,OAAO,CAAC,UAAUC,CAAC,EAAE;cAC3B,IAAIA,CAAC,CAACP,GAAG,KAAKG,MAAM,EAAE;gBACpBE,MAAM,GAAGE,CAAC;cACZ;YACF,CAAC,CAAC;YACF,IAAI,CAACF,MAAM,EAAE;cACXZ,OAAO,CAAC1F,IAAI,CACTsG,MAAM,GAAG;gBAAEL,GAAG,EAAEG,MAAM;gBAAEK,OAAO,EAAE,EAAE;gBAAEhF,OAAO,EAAE;cAAG,CACpD,CAAC;YACH;YACA6E,MAAM,CAACL,GAAG,CAAC,GAAGK,MAAM,CAACL,GAAG,CAAC,CAACS,MAAM,CAACL,UAAU,CAAC;UAC9C;UAEA,SAASM,eAAeA,CAACzH,IAAY,EAAEgH,GAAiB,EAAE;YACxD,OAAOjG,WAAC,CAACC,mBAAmB,CAC1BD,WAAC,CAACE,cAAc,CAACF,WAAC,CAACU,UAAU,CAACjB,WAAW,CAAC,EAAE,CAC1CO,WAAC,CAACG,aAAa,CAAClB,IAAI,CAAC,EACrBgH,GAAG,CACJ,CACH,CAAC;UACH;UAEA,MAAMvG,WAAW,GAAG,EAAE;UACtB,MAAMC,YAA4B,GAAG,EAAE;UAEvC,MAAMgH,IAAI,GAAGnH,IAAI,CAAC8C,GAAG,CAAC,MAAM,CAAC;UAE7B,KAAK,MAAM9C,IAAI,IAAImH,IAAI,EAAE;YACvB,IAAInH,IAAI,CAACoH,qBAAqB,CAAC,CAAC,EAAE;cAChClB,UAAU,CAAC3F,IAAI,CAACP,IAAI,CAACV,IAAI,CAAC;cAC1BgH,YAAY,CAAC/F,IAAI,CAACP,IAAI,CAAC;YACzB,CAAC,MAAM,IAAIA,IAAI,CAACqH,kBAAkB,CAAC,CAAC,EAAE;cACpChB,WAAW,CAAC9F,IAAI,CAACC,WAAC,CAAC2D,SAAS,CAACnE,IAAI,CAACV,IAAI,CAACgI,EAAE,CAAC,CAAC;cAC3CtH,IAAI,CAAC0D,WAAW,CACdlD,WAAC,CAACC,mBAAmB,CACnBD,WAAC,CAACqB,oBAAoB,CACpB,GAAG,EACHrB,WAAC,CAAC2D,SAAS,CAACnE,IAAI,CAACV,IAAI,CAACgI,EAAE,CAAC,EACzB9G,WAAC,CAAC+G,YAAY,CAACvH,IAAI,CAACV,IAAI,CAC1B,CACF,CACF,CAAC;YACH,CAAC,MAAM,IAAIU,IAAI,CAACwH,qBAAqB,CAAC,CAAC,EAAE;cAGvCxH,IAAI,CAACV,IAAI,CAACmI,IAAI,GAAG,KAAK;YACxB,CAAC,MAAM,IAAIzH,IAAI,CAAC0H,mBAAmB,CAAC,CAAC,EAAE;cACrC,MAAMf,MAAM,GAAG3G,IAAI,CAACV,IAAI,CAACqH,MAAM,CAAChH,KAAK;cACrC+G,UAAU,CAACC,MAAM,EAAE,SAAS,EAAE3G,IAAI,CAACV,IAAI,CAACsH,UAAU,CAAC;cACnD,KAAK,MAAMnH,IAAI,IAAIyD,MAAM,CAACC,IAAI,CAACnD,IAAI,CAACoD,qBAAqB,CAAC,CAAC,CAAC,EAAE;gBAC5D/B,KAAK,CAACsG,aAAa,CAAClI,IAAI,CAAC;gBACzB4G,WAAW,CAAC9F,IAAI,CAACC,WAAC,CAACU,UAAU,CAACzB,IAAI,CAAC,CAAC;cACtC;cACAO,IAAI,CAAC4H,MAAM,CAAC,CAAC;YACf,CAAC,MAAM,IAAI5H,IAAI,CAAC6H,sBAAsB,CAAC,CAAC,EAAE;cACxCnB,UAAU,CAAC1G,IAAI,CAACV,IAAI,CAACqH,MAAM,CAAChH,KAAK,EAAE,SAAS,EAAEK,IAAI,CAACV,IAAI,CAAC;cACxDU,IAAI,CAAC4H,MAAM,CAAC,CAAC;YACf,CAAC,MAAM,IAAI5H,IAAI,CAAC8H,0BAA0B,CAAC,CAAC,EAAE;cAC5C,MAAMC,MAAM,GAAG/H,IAAI,CAACV,IAAI,CAAC0I,WAAW;cACpC,IAAIxH,WAAC,CAAC6G,kBAAkB,CAACU,MAAM,CAAC,EAAE;gBAChC,MAAMT,EAAE,GAAGS,MAAM,CAACT,EAAE;gBACpB,IAAIA,EAAE,EAAE;kBACNpH,WAAW,CAACK,IAAI,CAAC,SAAS,CAAC;kBAC3BJ,YAAY,CAACI,IAAI,CAACc,KAAK,CAAC4G,kBAAkB,CAAC,CAAC,CAAC;kBAC7C5B,WAAW,CAAC9F,IAAI,CAACC,WAAC,CAAC2D,SAAS,CAACmD,EAAE,CAAC,CAAC;kBACjCf,aAAa,CAACe,EAAE,CAAC7H,IAAI,EAAE,SAAS,CAAC;kBACjCO,IAAI,CAAC0D,WAAW,CACdlD,WAAC,CAACC,mBAAmB,CACnBD,WAAC,CAACqB,oBAAoB,CACpB,GAAG,EACHrB,WAAC,CAAC2D,SAAS,CAACmD,EAAE,CAAC,EACf9G,WAAC,CAAC+G,YAAY,CAACQ,MAAM,CACvB,CACF,CACF,CAAC;gBACH,CAAC,MAAM;kBACL7H,WAAW,CAACK,IAAI,CAAC,SAAS,CAAC;kBAC3BJ,YAAY,CAACI,IAAI,CAACC,WAAC,CAAC+G,YAAY,CAACQ,MAAM,CAAC,CAAC;kBACzCzB,YAAY,CAAC/F,IAAI,CAACP,IAAI,CAAC;gBACzB;cACF,CAAC,MAAM,IAAIQ,WAAC,CAAC4G,qBAAqB,CAACW,MAAM,CAAC,EAAE;gBAC1C,MAAMT,EAAE,GAAGS,MAAM,CAACT,EAAE;gBACpB,IAAIA,EAAE,EAAE;kBACNpB,UAAU,CAAC3F,IAAI,CAACwH,MAAM,CAAC;kBACvB7H,WAAW,CAACK,IAAI,CAAC,SAAS,CAAC;kBAC3BJ,YAAY,CAACI,IAAI,CAACC,WAAC,CAAC2D,SAAS,CAACmD,EAAE,CAAC,CAAC;kBAClCf,aAAa,CAACe,EAAE,CAAC7H,IAAI,EAAE,SAAS,CAAC;gBACnC,CAAC,MAAM;kBACLS,WAAW,CAACK,IAAI,CAAC,SAAS,CAAC;kBAC3BJ,YAAY,CAACI,IAAI,CAACC,WAAC,CAAC+G,YAAY,CAACQ,MAAM,CAAC,CAAC;gBAC3C;gBACAzB,YAAY,CAAC/F,IAAI,CAACP,IAAI,CAAC;cACzB,CAAC,MAAM;gBAELA,IAAI,CAAC0D,WAAW,CAACwD,eAAe,CAAC,SAAS,EAAEa,MAAM,CAAC,CAAC;cACtD;YACF,CAAC,MAAM,IAAI/H,IAAI,CAACkI,wBAAwB,CAAC,CAAC,EAAE;cAC1C,MAAMH,MAAM,GAAG/H,IAAI,CAACV,IAAI,CAAC0I,WAAW;cAEpC,IAAID,MAAM,EAAE;gBACV/H,IAAI,CAAC0D,WAAW,CAACqE,MAAM,CAAC;gBAExB,IAAIvH,WAAC,CAAC2H,UAAU,CAACJ,MAAM,CAAC,EAAE;kBACxB,MAAMtI,IAAI,GAAGsI,MAAM,CAACT,EAAE,CAAC7H,IAAI;kBAC3B8G,aAAa,CAAC9G,IAAI,EAAEA,IAAI,CAAC;kBACzByG,UAAU,CAAC3F,IAAI,CAACwH,MAAM,CAAC;kBACvB7H,WAAW,CAACK,IAAI,CAACd,IAAI,CAAC;kBACtBU,YAAY,CAACI,IAAI,CAACC,WAAC,CAAC2D,SAAS,CAAC4D,MAAM,CAACT,EAAE,CAAC,CAAC;kBACzChB,YAAY,CAAC/F,IAAI,CAACP,IAAI,CAAC;gBACzB,CAAC,MAAM,IAAIQ,WAAC,CAAC4H,OAAO,CAACL,MAAM,CAAC,EAAE;kBAC5B,MAAMtI,IAAI,GAAGsI,MAAM,CAACT,EAAE,CAAC7H,IAAI;kBAC3BS,WAAW,CAACK,IAAI,CAACd,IAAI,CAAC;kBACtBU,YAAY,CAACI,IAAI,CAACc,KAAK,CAAC4G,kBAAkB,CAAC,CAAC,CAAC;kBAC7C5B,WAAW,CAAC9F,IAAI,CAACC,WAAC,CAAC2D,SAAS,CAAC4D,MAAM,CAACT,EAAE,CAAC,CAAC;kBACxCtH,IAAI,CAAC0D,WAAW,CACdlD,WAAC,CAACC,mBAAmB,CACnBD,WAAC,CAACqB,oBAAoB,CACpB,GAAG,EACHrB,WAAC,CAAC2D,SAAS,CAAC4D,MAAM,CAACT,EAAE,CAAC,EACtB9G,WAAC,CAAC+G,YAAY,CAACQ,MAAM,CACvB,CACF,CACF,CAAC;kBACDxB,aAAa,CAAC9G,IAAI,EAAEA,IAAI,CAAC;gBAC3B,CAAC,MAAM;kBACL,IAAIe,WAAC,CAACgH,qBAAqB,CAACO,MAAM,CAAC,EAAE;oBAGnCA,MAAM,CAACN,IAAI,GAAG,KAAK;kBACrB;kBACA,KAAK,MAAMhI,IAAI,IAAIyD,MAAM,CAACC,IAAI,CAC5B3C,WAAC,CAAC4C,qBAAqB,CAAC2E,MAAM,CAChC,CAAC,EAAE;oBACDxB,aAAa,CAAC9G,IAAI,EAAEA,IAAI,CAAC;kBAC3B;gBACF;cACF,CAAC,MAAM;gBACL,MAAMmH,UAAU,GAAG5G,IAAI,CAACV,IAAI,CAACsH,UAAU;gBACvC,IAAIA,UAAU,YAAVA,UAAU,CAAEtG,MAAM,EAAE;kBACtB,IAAIN,IAAI,CAACV,IAAI,CAACqH,MAAM,EAAE;oBACpBD,UAAU,CAAC1G,IAAI,CAACV,IAAI,CAACqH,MAAM,CAAChH,KAAK,EAAE,SAAS,EAAEiH,UAAU,CAAC;oBACzD5G,IAAI,CAAC4H,MAAM,CAAC,CAAC;kBACf,CAAC,MAAM;oBACL,MAAMS,KAAK,GAAG,EAAE;oBAEhB,KAAK,MAAMlD,SAAS,IAAIyB,UAAU,EAAE;sBAGlC,MAAM;wBAAE0B,KAAK;wBAAEC;sBAAS,CAAC,GAAGpD,SAAS;sBAErC,MAAMqD,OAAO,GAAGnH,KAAK,CAACgC,UAAU,CAACiF,KAAK,CAAC7I,IAAI,CAAC;sBAC5C,MAAM8D,YAAY,GAAGlE,sBAAsB,CACzCkJ,QAAQ,EACRhJ,gBACF,CAAC;sBAED,IACEiJ,OAAO,IACPhI,WAAC,CAAC4G,qBAAqB,CAACoB,OAAO,CAACxI,IAAI,CAACV,IAAI,CAAC,EAC1C;wBACAY,WAAW,CAACK,IAAI,CAACgD,YAAY,CAAC;wBAC9BpD,YAAY,CAACI,IAAI,CAACC,WAAC,CAAC2D,SAAS,CAACmE,KAAK,CAAC,CAAC;sBACvC,CAAC,MAEI,IAAI,CAACE,OAAO,EAAE;wBACjBH,KAAK,CAAC9H,IAAI,CAAC2G,eAAe,CAAC3D,YAAY,EAAE+E,KAAK,CAAC,CAAC;sBAClD;sBACA/B,aAAa,CAAC+B,KAAK,CAAC7I,IAAI,EAAE8D,YAAY,CAAC;oBACzC;oBAEAvD,IAAI,CAACyI,mBAAmB,CAACJ,KAAK,CAAC;kBACjC;gBACF,CAAC,MAAM;kBACLrI,IAAI,CAAC4H,MAAM,CAAC,CAAC;gBACf;cACF;YACF;UACF;UAEA3B,OAAO,CAACa,OAAO,CAAC,UAAUF,UAAU,EAAE;YACpC,MAAM8B,UAAU,GAAG,EAAE;YACrB,MAAMC,MAAM,GAAGtH,KAAK,CAACC,WAAW,CAACsF,UAAU,CAACJ,GAAG,CAAC;YAEhD,KAAK,IAAIrB,SAAS,IAAIyB,UAAU,CAACI,OAAO,EAAE;cACxC,IAAIxG,WAAC,CAACoI,0BAA0B,CAACzD,SAAS,CAAC,EAAE;gBAC3CuD,UAAU,CAACnI,IAAI,CACbC,WAAC,CAACC,mBAAmB,CACnBD,WAAC,CAACqB,oBAAoB,CACpB,GAAG,EACHsD,SAAS,CAACmD,KAAK,EACf9H,WAAC,CAACU,UAAU,CAACyH,MAAM,CACrB,CACF,CACF,CAAC;cACH,CAAC,MAAM,IAAInI,WAAC,CAACqI,wBAAwB,CAAC1D,SAAS,CAAC,EAAE;gBAChDA,SAAS,GAAG3E,WAAC,CAACsI,eAAe,CAC3B3D,SAAS,CAACmD,KAAK,EACf9H,WAAC,CAACU,UAAU,CAAC,SAAS,CACxB,CAAC;cACH;cAEA,IAAIV,WAAC,CAACuI,iBAAiB,CAAC5D,SAAS,CAAC,EAAE;gBAClC,MAAM;kBAAE6D;gBAAS,CAAC,GAAG7D,SAAS;gBAC9BuD,UAAU,CAACnI,IAAI,CACbC,WAAC,CAACC,mBAAmB,CACnBD,WAAC,CAACqB,oBAAoB,CACpB,GAAG,EACHsD,SAAS,CAACmD,KAAK,EACf9H,WAAC,CAACsB,gBAAgB,CAChBtB,WAAC,CAACU,UAAU,CAACyH,MAAM,CAAC,EACpBxD,SAAS,CAAC6D,QAAQ,EACHA,QAAQ,CAACxJ,IAAI,KAAK,eACnC,CACF,CACF,CACF,CAAC;cACH;YACF;YAEA,IAAIoH,UAAU,CAAC5E,OAAO,CAAC1B,MAAM,EAAE;cAC7B,MAAMJ,WAAW,GAAG,EAAE;cACtB,MAAMC,YAAY,GAAG,EAAE;cACvB,IAAI8I,aAAa,GAAG,KAAK;cAEzB,KAAK,MAAM3J,IAAI,IAAIsH,UAAU,CAAC5E,OAAO,EAAE;gBACrC,IAAIxB,WAAC,CAACqH,sBAAsB,CAACvI,IAAI,CAAC,EAAE;kBAClC2J,aAAa,GAAG,IAAI;gBACtB,CAAC,MAAM,IAAIzI,WAAC,CAAC0I,iBAAiB,CAAC5J,IAAI,CAAC,EAAE;kBACpC,MAAMiE,YAAY,GAAGlE,sBAAsB,CACzCC,IAAI,CAACiJ,QAAQ,EACbhJ,gBACF,CAAC;kBACDW,WAAW,CAACK,IAAI,CAACgD,YAAY,CAAC;kBAC9BpD,YAAY,CAACI,IAAI,CACfC,WAAC,CAACsB,gBAAgB,CAChBtB,WAAC,CAACU,UAAU,CAACyH,MAAM,CAAC,EACpBrJ,IAAI,CAACgJ,KAAK,EACV9H,WAAC,CAAC2I,eAAe,CAAC7J,IAAI,CAACgJ,KAAK,CAC9B,CACF,CAAC;gBACH,CAAC,MAAM,CAEP;cACF;cAEAI,UAAU,CAACnI,IAAI,CACb,GAAGR,mBAAmB,CACpBC,IAAI,EACJQ,WAAC,CAACU,UAAU,CAACjB,WAAW,CAAC,EACzBC,WAAW,EACXC,YAAY,EACZ8I,aAAa,GAAGzI,WAAC,CAACU,UAAU,CAACyH,MAAM,CAAC,GAAG,IAAI,EAC3CpJ,gBACF,CACF,CAAC;YACH;YAEA6G,OAAO,CAAC7F,IAAI,CAACC,WAAC,CAACG,aAAa,CAACiG,UAAU,CAACJ,GAAG,CAAC,CAAC;YAC7CL,OAAO,CAAC5F,IAAI,CACVC,WAAC,CAAC4I,kBAAkB,CAClB,IAAI,EACJ,CAAC5I,WAAC,CAACU,UAAU,CAACyH,MAAM,CAAC,CAAC,EACtBnI,WAAC,CAAC6I,cAAc,CAACX,UAAU,CAC7B,CACF,CAAC;UACH,CAAC,CAAC;UAEF,IAAIY,UAAU,GAAG,IAAAC,qCAAa,EAAC,IAAI,CAAChF,IAAI,CAACiF,IAAI,EAAEpH,OAAO,CAAC;UAEvD,IAAIkH,UAAU,EAAEA,UAAU,GAAG9I,WAAC,CAACG,aAAa,CAAC2I,UAAU,CAAC;UAES;YAAA,IAAAG,WAAA,EAAAC,qBAAA;YAE/D,CAAAA,qBAAA,IAAAD,WAAA,GAAAzJ,IAAI,CAACqB,KAAK,EAACsI,cAAc,YAAAD,qBAAA,GAAzBD,WAAA,CAAWE,cAAc,GAEvBhL,OAAO,CAAC,iBAAiB,CAAC,CAACiL,KAAK,CAACC,SAAS,CAACF,cAAc;UAC7D;UAEA3J,IAAI,CAACqB,KAAK,CAACsI,cAAc,CAAC,CAACrC,EAAE,EAAEwC,OAAO,KAAK;YACzCzD,WAAW,CAAC9F,IAAI,CAAC+G,EAAE,CAAC;YACpB,IAAI,CAACwC,OAAO,IAAIxC,EAAE,CAAC7H,IAAI,IAAIsG,SAAS,EAAE;cACpC,KAAK,MAAMwC,QAAQ,IAAIxC,SAAS,CAACuB,EAAE,CAAC7H,IAAI,CAAC,EAAE;gBACzCS,WAAW,CAACK,IAAI,CAACgI,QAAQ,CAAC;gBAC1BpI,YAAY,CAACI,IAAI,CAACC,WAAC,CAACyH,kBAAkB,CAAC,CAAC,CAAC;cAC3C;YACF;UACF,CAAC,CAAC;UAEF,IAAI5B,WAAW,CAAC/F,MAAM,EAAE;YACtB4F,UAAU,CAAC6D,OAAO,CAChBvJ,WAAC,CAACe,mBAAmB,CACnB,KAAK,EACL8E,WAAW,CAAC2D,GAAG,CAAC1C,EAAE,IAAI9G,WAAC,CAACgB,kBAAkB,CAAC8F,EAAE,CAAC,CAChD,CACF,CAAC;UACH;UAEA,IAAIpH,WAAW,CAACI,MAAM,EAAE;YACtB4F,UAAU,CAAC3F,IAAI,CACb,GAAGR,mBAAmB,CACpBC,IAAI,EACJQ,WAAC,CAACU,UAAU,CAACjB,WAAW,CAAC,EACzBC,WAAW,EACXC,YAAY,EACZ,IAAI,EACJZ,gBACF,CACF,CAAC;UACH;UAEAS,IAAI,CAACiK,QAAQ,CAACvH,mBAAmB,EAAE;YACjCV,OAAO,EAAE+D,SAAS;YAClBvC,SAAS,EAAE0D,eAAe;YAC1B7F;UACF,CAAC,CAAC;UAEF,KAAK,MAAMrB,IAAI,IAAIsG,YAAY,EAAE;YAC/BtG,IAAI,CAAC4H,MAAM,CAAC,CAAC;UACf;UAEA,IAAIsC,MAAM,GAAG,KAAK;UAClBlK,IAAI,CAACiK,QAAQ,CAAC;YACZE,eAAeA,CAACnK,IAAI,EAAE;cACpBkK,MAAM,GAAG,IAAI;cACblK,IAAI,CAACoK,IAAI,CAAC,CAAC;YACb,CAAC;YACDC,QAAQA,CAACrK,IAAI,EAAE;cACbA,IAAI,CAACsK,IAAI,CAAC,CAAC;YACb,CAAC;YAEDC,OAAO,EAAE;UACX,CAAC,CAAC;UAEFvK,IAAI,CAACV,IAAI,CAAC6H,IAAI,GAAG,CACfpI,aAAa,CAAC;YACZyL,eAAe,EAAEhK,WAAC,CAACsB,gBAAgB,CACjCtB,WAAC,CAACU,UAAU,CAACoB,YAAY,CAAC,EAC1B9B,WAAC,CAACU,UAAU,CAAC,UAAU,CACzB,CAAC;YACDuJ,WAAW,EAAEvE,UAAU;YACvBwE,WAAW,EAAEpB,UAAU;YACvBqB,OAAO,EAAEnK,WAAC,CAACoK,eAAe,CAACzE,OAAO,CAAC;YACnC0E,OAAO,EAAErK,WAAC,CAAC4I,kBAAkB,CAC3B,IAAI,EACJ,EAAE,EACF5I,WAAC,CAAC6I,cAAc,CAACrJ,IAAI,CAACV,IAAI,CAAC6H,IAAI,CAAC,EAChC,KAAK,EACL+C,MACF,CAAC;YACDY,OAAO,EAAEtK,WAAC,CAACoK,eAAe,CAACxE,OAAO,CAAC;YACnC2E,iBAAiB,EAAEvK,WAAC,CAACU,UAAU,CAACjB,WAAW,CAAC;YAC5C+K,kBAAkB,EAAExK,WAAC,CAACU,UAAU,CAACkE,YAAY;UAC/C,CAAC,CAAC,CACH;UACDpF,IAAI,CAACiL,OAAO,CAACjL,IAAI,CAAC8C,GAAG,CAAC,QAAQ,CAAC,CAAC;QAClC;MACF;IACF;EACF,CAAC;AACH,CAAC,CAAC", "ignoreList": []}