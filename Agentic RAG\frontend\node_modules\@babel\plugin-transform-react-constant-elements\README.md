# @babel/plugin-transform-react-constant-elements

> Treat React JSX elements as value types and hoist them to the highest scope

See our website [@babel/plugin-transform-react-constant-elements](https://babeljs.io/docs/babel-plugin-transform-react-constant-elements) for more information.

## Install

Using npm:

```sh
npm install --save-dev @babel/plugin-transform-react-constant-elements
```

or using yarn:

```sh
yarn add @babel/plugin-transform-react-constant-elements --dev
```
