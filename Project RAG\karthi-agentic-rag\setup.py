#!/usr/bin/env python3
"""
Setup script for <PERSON><PERSON><PERSON>'s Agentic RAG application.
Automates the installation and configuration process.
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def run_command(command, cwd=None, check=True):
    """Run a shell command and return the result."""
    print(f"Running: {command}")
    try:
        result = subprocess.run(
            command, 
            shell=True, 
            cwd=cwd, 
            check=check,
            capture_output=True,
            text=True
        )
        if result.stdout:
            print(result.stdout)
        return result
    except subprocess.CalledProcessError as e:
        print(f"Error running command: {e}")
        if e.stderr:
            print(f"Error output: {e.stderr}")
        return None

def check_python_version():
    """Check if Python version is 3.8 or higher."""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ Python 3.8 or higher is required!")
        print(f"Current version: {version.major}.{version.minor}.{version.micro}")
        return False
    print(f"✅ Python {version.major}.{version.minor}.{version.micro} detected")
    return True

def check_node_version():
    """Check if Node.js is installed."""
    result = run_command("node --version", check=False)
    if result and result.returncode == 0:
        version = result.stdout.strip()
        print(f"✅ Node.js {version} detected")
        return True
    else:
        print("❌ Node.js not found! Please install Node.js 16+ from https://nodejs.org/")
        return False

def check_tesseract():
    """Check if Tesseract OCR is installed."""
    result = run_command("tesseract --version", check=False)
    if result and result.returncode == 0:
        version_line = result.stdout.split('\n')[0]
        print(f"✅ {version_line}")
        return True
    else:
        print("⚠️  Tesseract OCR not found!")
        print("Please install Tesseract OCR:")
        print("  Windows: https://github.com/UB-Mannheim/tesseract/wiki")
        print("  macOS: brew install tesseract")
        print("  Ubuntu: sudo apt-get install tesseract-ocr")
        return False

def setup_backend():
    """Set up the backend environment."""
    print("\n🔧 Setting up backend...")
    
    backend_dir = Path("backend")
    if not backend_dir.exists():
        print("❌ Backend directory not found!")
        return False
    
    # Create virtual environment
    venv_path = backend_dir / "venv"
    if not venv_path.exists():
        print("Creating virtual environment...")
        result = run_command(f"python -m venv {venv_path}", cwd=backend_dir)
        if not result:
            return False
    
    # Determine activation script
    if os.name == 'nt':  # Windows
        activate_script = venv_path / "Scripts" / "activate.bat"
        pip_path = venv_path / "Scripts" / "pip"
    else:  # Unix-like
        activate_script = venv_path / "bin" / "activate"
        pip_path = venv_path / "bin" / "pip"
    
    # Install requirements
    print("Installing Python dependencies...")
    result = run_command(f"{pip_path} install -r requirements.txt", cwd=backend_dir)
    if not result:
        return False
    
    print("✅ Backend setup complete!")
    return True

def setup_frontend():
    """Set up the frontend environment."""
    print("\n🎨 Setting up frontend...")
    
    frontend_dir = Path("frontend")
    if not frontend_dir.exists():
        print("❌ Frontend directory not found!")
        return False
    
    # Install npm dependencies
    print("Installing Node.js dependencies...")
    result = run_command("npm install", cwd=frontend_dir)
    if not result:
        return False
    
    print("✅ Frontend setup complete!")
    return True

def setup_environment():
    """Set up environment variables."""
    print("\n🔐 Setting up environment...")
    
    env_file = Path(".env")
    env_example = Path(".env.example")
    
    if not env_file.exists() and env_example.exists():
        print("Copying .env.example to .env...")
        shutil.copy(env_example, env_file)
        print("⚠️  Please edit .env file and add your Hugging Face token!")
        print("   Get your token from: https://huggingface.co/settings/tokens")
    elif env_file.exists():
        print("✅ .env file already exists")
    else:
        print("❌ No .env.example file found!")
        return False
    
    return True

def create_directories():
    """Create necessary directories."""
    print("\n📁 Creating directories...")
    
    directories = [
        "data/uploads",
        "data/embeddings"
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        print(f"✅ Created {directory}")
    
    return True

def print_next_steps():
    """Print instructions for running the application."""
    print("\n" + "="*60)
    print("🎉 SETUP COMPLETE!")
    print("="*60)
    print("\n📝 Next Steps:")
    print("\n1. Edit the .env file and add your Hugging Face token:")
    print("   HF_TOKEN=your_token_here")
    print("\n2. Start the backend server:")
    if os.name == 'nt':  # Windows
        print("   cd backend")
        print("   venv\\Scripts\\activate")
        print("   python run.py")
    else:  # Unix-like
        print("   cd backend")
        print("   source venv/bin/activate")
        print("   python run.py")
    
    print("\n3. In a new terminal, start the frontend:")
    print("   cd frontend")
    print("   npm start")
    
    print("\n4. Open your browser and go to:")
    print("   http://localhost:3000")
    
    print("\n5. Login with:")
    print("   Username: karthick")
    print("   Password: 1858")
    
    print("\n🚀 Happy RAG-ing!")

def main():
    """Main setup function."""
    print("🤖 Karthi's Agentic RAG Setup")
    print("="*40)
    
    # Check prerequisites
    print("\n🔍 Checking prerequisites...")
    
    if not check_python_version():
        return False
    
    if not check_node_version():
        return False
    
    check_tesseract()  # Warning only, not required for basic functionality
    
    # Setup components
    if not setup_environment():
        return False
    
    if not create_directories():
        return False
    
    if not setup_backend():
        return False
    
    if not setup_frontend():
        return False
    
    # Print next steps
    print_next_steps()
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n❌ Setup interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n❌ Setup failed with error: {e}")
        sys.exit(1)
