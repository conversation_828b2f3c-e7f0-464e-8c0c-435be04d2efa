{"version": 3, "names": ["Object", "defineProperty", "exports", "value", "getVisitor", "_assert", "require", "_hoist", "_emit", "_replaceShorthandObjectMethod", "util", "t", "Method", "path", "state", "node", "shouldRegenerate", "container", "functionExpression", "cloneNode", "body", "generator", "async", "get", "set", "returnStatement", "callExpression", "unwrapFunctionEnvironment", "Function", "exit", "wrapWithTypes", "replaceShorthandObjectMethod", "contextId", "scope", "generateUidIdentifier", "argsId", "ensureBlock", "body<PERSON>lockPath", "traverse", "await<PERSON><PERSON>tor", "functionSentVisitor", "context", "pluginPass", "outerBody", "innerBody", "for<PERSON>ach", "child<PERSON><PERSON>", "isExpressionStatement", "isStringLiteral", "expression", "push", "_blockHoist", "length", "outerFnExpr", "getOuterFnExpr", "assertIdentifier", "id", "vars", "hoist", "usesThis", "usesArguments", "getArgsId", "clone", "argumentsThisVisitor", "variableDeclarator", "identifier", "emitter", "Emitter", "explode", "variableDeclaration", "wrapArgs", "getContextFunction", "tryLocsList", "getTryLocsList", "nullLiteral", "thisExpression", "currentScope", "hasOwnBinding", "rename", "parent", "wrapCall", "newHelpersAvailable", "memberExpression", "addHelper", "runtimeProperty", "blockStatement", "p", "registerDeclaration", "oldDirectives", "directives", "wasGeneratorFunction", "isExpression", "replaceWithOrRemove", "addComment", "insertedLocs", "getInsertedLocs", "NumericLiteral", "has", "replaceWith", "numericLiteral", "requeue", "opts", "asyncGenerators", "generators", "funPath", "getTypes", "assertFunction", "isFunctionDeclaration", "getMarkedFunctionId", "markInfo", "WeakMap", "getMarkInfo", "blockPath", "findParent", "isProgram", "isBlockStatement", "block", "assert", "ok", "Array", "isArray", "info", "decl", "unshiftContainer", "decl<PERSON>ath", "strictEqual", "markedId", "markCallExp", "index", "declarations", "markCallExpPath", "FunctionExpression|FunctionDeclaration|Method", "skip", "Identifier", "name", "isReference", "ThisExpression", "MetaProperty", "meta", "property", "AwaitExpression", "argument", "helper", "yieldExpression"], "sources": ["../../src/regenerator/visit.ts"], "sourcesContent": ["\"use strict\";\n\nimport assert from \"node:assert\";\nimport { hoist } from \"./hoist.ts\";\nimport { Emitter } from \"./emit.ts\";\nimport replaceShorthandObjectMethod from \"./replaceShorthandObjectMethod.ts\";\nimport * as util from \"./util.ts\";\nimport type { PluginPass, Visitor } from \"@babel/core\";\n\nexport const getVisitor = (t: any): Visitor<PluginPass> => ({\n  Method(path: any, state: any) {\n    const node = path.node;\n\n    if (!shouldRegenerate(node, state)) return;\n\n    const container = t.functionExpression(\n      null,\n      [],\n      t.cloneNode(node.body, false),\n      node.generator,\n      node.async,\n    );\n\n    path\n      .get(\"body\")\n      .set(\"body\", [t.returnStatement(t.callExpression(container, []))]);\n\n    // Regardless of whether or not the wrapped function is a an async method\n    // or generator the outer function should not be\n    node.async = false;\n    node.generator = false;\n\n    // Unwrap the wrapper IIFE's environment so super and this and such still work.\n    path.get(\"body.body.0.argument.callee\").unwrapFunctionEnvironment();\n  },\n  Function: {\n    exit: util.wrapWithTypes(t, function (path: any, state: any) {\n      let node = path.node;\n\n      if (!shouldRegenerate(node, state)) return;\n\n      // if this is an ObjectMethod, we need to convert it to an ObjectProperty\n      path = replaceShorthandObjectMethod(path);\n      node = path.node;\n\n      const contextId = path.scope.generateUidIdentifier(\"context\");\n      const argsId = path.scope.generateUidIdentifier(\"args\");\n\n      path.ensureBlock();\n      const bodyBlockPath = path.get(\"body\");\n\n      if (node.async) {\n        // eslint-disable-next-line @typescript-eslint/no-use-before-define\n        bodyBlockPath.traverse(awaitVisitor, this);\n      }\n\n      // eslint-disable-next-line @typescript-eslint/no-use-before-define\n      bodyBlockPath.traverse(functionSentVisitor, {\n        context: contextId,\n        pluginPass: this,\n      });\n\n      const outerBody: any[] = [];\n      const innerBody: any[] = [];\n\n      bodyBlockPath.get(\"body\").forEach(function (childPath: any) {\n        const node = childPath.node;\n        if (\n          t.isExpressionStatement(node) &&\n          t.isStringLiteral(node.expression)\n        ) {\n          // Babylon represents directives like \"use strict\" as elements\n          // of a bodyBlockPath.node.directives array, but they could just\n          // as easily be represented (by other parsers) as traditional\n          // string-literal-valued expression statements, so we need to\n          // handle that here. (#248)\n          outerBody.push(node);\n        } else if (node?._blockHoist != null) {\n          outerBody.push(node);\n        } else {\n          innerBody.push(node);\n        }\n      });\n\n      if (outerBody.length > 0) {\n        // Only replace the inner body if we actually hoisted any statements\n        // to the outer body.\n        bodyBlockPath.node.body = innerBody;\n      }\n\n      const outerFnExpr = getOuterFnExpr(this, path);\n      // Note that getOuterFnExpr has the side-effect of ensuring that the\n      // function has a name (so node.id will always be an Identifier), even\n      // if a temporary name has to be synthesized.\n      t.assertIdentifier(node.id);\n\n      // Turn all declarations into vars, and replace the original\n      // declarations with equivalent assignment expressions.\n      const vars = hoist(path);\n\n      const context = {\n        usesThis: false,\n        usesArguments: false,\n        getArgsId: () => t.clone(argsId),\n      };\n      // eslint-disable-next-line @typescript-eslint/no-use-before-define\n      path.traverse(argumentsThisVisitor, context);\n\n      if (context.usesArguments) {\n        vars.push(\n          t.variableDeclarator(t.clone(argsId), t.identifier(\"arguments\")),\n        );\n      }\n\n      const emitter = new Emitter(contextId, path.scope, vars, this);\n      emitter.explode(path.get(\"body\"));\n\n      if (vars.length > 0) {\n        outerBody.push(t.variableDeclaration(\"var\", vars));\n      }\n\n      const wrapArgs: any[] = [emitter.getContextFunction()];\n      const tryLocsList = emitter.getTryLocsList();\n\n      if (node.generator) {\n        wrapArgs.push(outerFnExpr);\n      } else if (context.usesThis || tryLocsList || node.async) {\n        // Async functions that are not generators don't care about the\n        // outer function because they don't need it to be marked and don't\n        // inherit from its .prototype.\n        wrapArgs.push(t.nullLiteral());\n      }\n      if (context.usesThis) {\n        wrapArgs.push(t.thisExpression());\n      } else if (tryLocsList || node.async) {\n        wrapArgs.push(t.nullLiteral());\n      }\n      if (tryLocsList) {\n        wrapArgs.push(tryLocsList);\n      } else if (node.async) {\n        wrapArgs.push(t.nullLiteral());\n      }\n\n      if (node.async) {\n        // Rename any locally declared \"Promise\" variable,\n        // to use the global one.\n        let currentScope = path.scope;\n        do {\n          if (currentScope.hasOwnBinding(\"Promise\"))\n            currentScope.rename(\"Promise\");\n        } while ((currentScope = currentScope.parent));\n\n        wrapArgs.push(t.identifier(\"Promise\"));\n      }\n\n      const wrapCall = t.callExpression(\n        process.env.BABEL_8_BREAKING || util.newHelpersAvailable(this)\n          ? !node.async\n            ? t.memberExpression(\n                t.callExpression(this.addHelper(\"regenerator\"), []),\n                t.identifier(\"w\"),\n              )\n            : node.generator\n              ? this.addHelper(\"regeneratorAsyncGen\")\n              : this.addHelper(\"regeneratorAsync\")\n          : util.runtimeProperty(this, node.async ? \"async\" : \"wrap\"),\n        wrapArgs,\n      );\n\n      outerBody.push(t.returnStatement(wrapCall));\n      node.body = t.blockStatement(outerBody);\n      // We injected a few new variable declarations (for every hoisted var),\n      // so we need to add them to the scope.\n      path.get(\"body.body\").forEach((p: any) => p.scope.registerDeclaration(p));\n\n      const oldDirectives = bodyBlockPath.node.directives;\n      if (oldDirectives) {\n        // Babylon represents directives like \"use strict\" as elements of\n        // a bodyBlockPath.node.directives array. (#248)\n        node.body.directives = oldDirectives;\n      }\n\n      const wasGeneratorFunction = node.generator;\n      if (wasGeneratorFunction) {\n        node.generator = false;\n      }\n\n      if (node.async) {\n        node.async = false;\n      }\n\n      if (wasGeneratorFunction && t.isExpression(node)) {\n        util.replaceWithOrRemove(\n          path,\n          t.callExpression(\n            process.env.BABEL_8_BREAKING || util.newHelpersAvailable(this)\n              ? t.memberExpression(\n                  t.callExpression(this.addHelper(\"regenerator\"), []),\n                  t.identifier(\"m\"),\n                )\n              : util.runtimeProperty(this, \"mark\"),\n            [node],\n          ),\n        );\n        path.addComment(\"leading\", \"#__PURE__\");\n      }\n\n      const insertedLocs = emitter.getInsertedLocs();\n\n      path.traverse({\n        NumericLiteral(path: any) {\n          if (!insertedLocs.has(path.node)) {\n            return;\n          }\n\n          path.replaceWith(t.numericLiteral(path.node.value));\n        },\n      });\n\n      // Generators are processed in 'exit' handlers so that regenerator only has to run on\n      // an ES5 AST, but that means traversal will not pick up newly inserted references\n      // to things like 'regeneratorRuntime'. To avoid this, we explicitly requeue.\n      path.requeue();\n    }),\n  },\n});\n\n// Check if a node should be transformed by regenerator\nfunction shouldRegenerate(node: any, state: any) {\n  if (node.generator) {\n    if (node.async) {\n      // Async generator\n      return state.opts.asyncGenerators !== false;\n    } else {\n      // Plain generator\n      return state.opts.generators !== false;\n    }\n  } else if (node.async) {\n    // Async function\n    return state.opts.async !== false;\n  } else {\n    // Not a generator or async function.\n    return false;\n  }\n}\n\n// Given a NodePath for a Function, return an Expression node that can be\n// used to refer reliably to the function object from inside the function.\n// This expression is essentially a replacement for arguments.callee, with\n// the key advantage that it works in strict mode.\nfunction getOuterFnExpr(state: PluginPass, funPath: any) {\n  const t = util.getTypes();\n  const node = funPath.node;\n  t.assertFunction(node);\n\n  if (!node.id) {\n    // Default-exported function declarations, and function expressions may not\n    // have a name to reference, so we explicitly add one.\n    node.id = funPath.scope.parent.generateUidIdentifier(\"callee\");\n  }\n\n  if (\n    node.generator && // Non-generator functions don't need to be marked.\n    t.isFunctionDeclaration(node)\n  ) {\n    // Return the identifier returned by runtime.mark(<node.id>).\n    return getMarkedFunctionId(state, funPath);\n  }\n\n  return t.clone(node.id);\n}\n\nconst markInfo = new WeakMap();\n\nfunction getMarkInfo(node: any) {\n  if (!markInfo.has(node)) {\n    markInfo.set(node, {});\n  }\n  return markInfo.get(node);\n}\n\nfunction getMarkedFunctionId(state: PluginPass, funPath: any) {\n  const t = util.getTypes();\n  const node = funPath.node;\n  t.assertIdentifier(node.id);\n\n  const blockPath = funPath.findParent(function (path: any) {\n    return path.isProgram() || path.isBlockStatement();\n  });\n\n  if (!blockPath) {\n    return node.id;\n  }\n\n  const block = blockPath.node;\n  assert.ok(Array.isArray(block.body));\n\n  const info = getMarkInfo(block);\n  if (!info.decl) {\n    info.decl = t.variableDeclaration(\"var\", []);\n    blockPath.unshiftContainer(\"body\", info.decl);\n    info.declPath = blockPath.get(\"body.0\");\n  }\n\n  assert.strictEqual(info.declPath.node, info.decl);\n\n  // Get a new unique identifier for our marked variable.\n  const markedId = blockPath.scope.generateUidIdentifier(\"marked\");\n  const markCallExp = t.callExpression(\n    process.env.BABEL_8_BREAKING || util.newHelpersAvailable(state)\n      ? t.memberExpression(\n          t.callExpression(state.addHelper(\"regenerator\"), []),\n          t.identifier(\"m\"),\n        )\n      : util.runtimeProperty(state, \"mark\"),\n    [t.clone(node.id)],\n  );\n\n  const index =\n    info.decl.declarations.push(t.variableDeclarator(markedId, markCallExp)) -\n    1;\n\n  const markCallExpPath = info.declPath.get(\"declarations.\" + index + \".init\");\n\n  assert.strictEqual(markCallExpPath.node, markCallExp);\n\n  markCallExpPath.addComment(\"leading\", \"#__PURE__\");\n\n  return t.clone(markedId);\n}\n\nconst argumentsThisVisitor = {\n  \"FunctionExpression|FunctionDeclaration|Method\": function (path: any) {\n    path.skip();\n  },\n\n  Identifier: function (path: any, state: any) {\n    if (path.node.name === \"arguments\" && util.isReference(path)) {\n      util.replaceWithOrRemove(path, state.getArgsId());\n      state.usesArguments = true;\n    }\n  },\n\n  ThisExpression: function (path: any, state: any) {\n    state.usesThis = true;\n  },\n};\n\nconst functionSentVisitor = {\n  MetaProperty(path: any, state: any) {\n    const { node } = path;\n\n    if (node.meta.name === \"function\" && node.property.name === \"sent\") {\n      const t = util.getTypes();\n      util.replaceWithOrRemove(\n        path,\n        t.memberExpression(\n          t.clone((this as any).context),\n          t.identifier(\n            process.env.BABEL_8_BREAKING ||\n              util.newHelpersAvailable(state.pluginPass)\n              ? \"v\"\n              : \"_sent\",\n          ),\n        ),\n      );\n    }\n  },\n};\n\nconst awaitVisitor: Visitor<PluginPass> = {\n  Function: function (path: any) {\n    path.skip(); // Don't descend into nested function scopes.\n  },\n\n  AwaitExpression: function (path: any) {\n    const t = util.getTypes();\n\n    // Convert await expressions to yield expressions.\n    const argument = path.node.argument;\n\n    const helper =\n      // This is slightly tricky: newer versions of the `regeneratorRuntime`\n      // helper support using `awaitAsyncGenerator` as an alternative to\n      // `regeneratorRuntime().awrap`. There is no direct way to test if we\n      // have that part of the helper available, but we know that it has been\n      // introduced in the same version as `regeneratorKeys`.\n      process.env.BABEL_8_BREAKING || util.newHelpersAvailable(this)\n        ? this.addHelper(\"awaitAsyncGenerator\")\n        : util.runtimeProperty(this, \"awrap\");\n\n    // Transforming `await x` to `yield regeneratorRuntime.awrap(x)`\n    // causes the argument to be wrapped in such a way that the runtime\n    // can distinguish between awaited and merely yielded values.\n    util.replaceWithOrRemove(\n      path,\n      t.yieldExpression(t.callExpression(helper, [argument]), false),\n    );\n  },\n};\n"], "mappings": "AAAA,YAAY;;AAACA,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,UAAA;AAEb,IAAAC,OAAA,GAAAC,OAAA;AACA,IAAAC,MAAA,GAAAD,OAAA;AACA,IAAAE,KAAA,GAAAF,OAAA;AACA,IAAAG,6BAAA,GAAAH,OAAA;AACA,IAAAI,IAAA,GAAAJ,OAAA;AAGO,MAAMF,UAAU,GAAIO,CAAM,KAA2B;EAC1DC,MAAMA,CAACC,IAAS,EAAEC,KAAU,EAAE;IAC5B,MAAMC,IAAI,GAAGF,IAAI,CAACE,IAAI;IAEtB,IAAI,CAACC,gBAAgB,CAACD,IAAI,EAAED,KAAK,CAAC,EAAE;IAEpC,MAAMG,SAAS,GAAGN,CAAC,CAACO,kBAAkB,CACpC,IAAI,EACJ,EAAE,EACFP,CAAC,CAACQ,SAAS,CAACJ,IAAI,CAACK,IAAI,EAAE,KAAK,CAAC,EAC7BL,IAAI,CAACM,SAAS,EACdN,IAAI,CAACO,KACP,CAAC;IAEDT,IAAI,CACDU,GAAG,CAAC,MAAM,CAAC,CACXC,GAAG,CAAC,MAAM,EAAE,CAACb,CAAC,CAACc,eAAe,CAACd,CAAC,CAACe,cAAc,CAACT,SAAS,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;IAIpEF,IAAI,CAACO,KAAK,GAAG,KAAK;IAClBP,IAAI,CAACM,SAAS,GAAG,KAAK;IAGtBR,IAAI,CAACU,GAAG,CAAC,6BAA6B,CAAC,CAACI,yBAAyB,CAAC,CAAC;EACrE,CAAC;EACDC,QAAQ,EAAE;IACRC,IAAI,EAAEnB,IAAI,CAACoB,aAAa,CAACnB,CAAC,EAAE,UAAUE,IAAS,EAAEC,KAAU,EAAE;MAC3D,IAAIC,IAAI,GAAGF,IAAI,CAACE,IAAI;MAEpB,IAAI,CAACC,gBAAgB,CAACD,IAAI,EAAED,KAAK,CAAC,EAAE;MAGpCD,IAAI,GAAG,IAAAkB,qCAA4B,EAAClB,IAAI,CAAC;MACzCE,IAAI,GAAGF,IAAI,CAACE,IAAI;MAEhB,MAAMiB,SAAS,GAAGnB,IAAI,CAACoB,KAAK,CAACC,qBAAqB,CAAC,SAAS,CAAC;MAC7D,MAAMC,MAAM,GAAGtB,IAAI,CAACoB,KAAK,CAACC,qBAAqB,CAAC,MAAM,CAAC;MAEvDrB,IAAI,CAACuB,WAAW,CAAC,CAAC;MAClB,MAAMC,aAAa,GAAGxB,IAAI,CAACU,GAAG,CAAC,MAAM,CAAC;MAEtC,IAAIR,IAAI,CAACO,KAAK,EAAE;QAEde,aAAa,CAACC,QAAQ,CAACC,YAAY,EAAE,IAAI,CAAC;MAC5C;MAGAF,aAAa,CAACC,QAAQ,CAACE,mBAAmB,EAAE;QAC1CC,OAAO,EAAET,SAAS;QAClBU,UAAU,EAAE;MACd,CAAC,CAAC;MAEF,MAAMC,SAAgB,GAAG,EAAE;MAC3B,MAAMC,SAAgB,GAAG,EAAE;MAE3BP,aAAa,CAACd,GAAG,CAAC,MAAM,CAAC,CAACsB,OAAO,CAAC,UAAUC,SAAc,EAAE;QAC1D,MAAM/B,IAAI,GAAG+B,SAAS,CAAC/B,IAAI;QAC3B,IACEJ,CAAC,CAACoC,qBAAqB,CAAChC,IAAI,CAAC,IAC7BJ,CAAC,CAACqC,eAAe,CAACjC,IAAI,CAACkC,UAAU,CAAC,EAClC;UAMAN,SAAS,CAACO,IAAI,CAACnC,IAAI,CAAC;QACtB,CAAC,MAAM,IAAI,CAAAA,IAAI,oBAAJA,IAAI,CAAEoC,WAAW,KAAI,IAAI,EAAE;UACpCR,SAAS,CAACO,IAAI,CAACnC,IAAI,CAAC;QACtB,CAAC,MAAM;UACL6B,SAAS,CAACM,IAAI,CAACnC,IAAI,CAAC;QACtB;MACF,CAAC,CAAC;MAEF,IAAI4B,SAAS,CAACS,MAAM,GAAG,CAAC,EAAE;QAGxBf,aAAa,CAACtB,IAAI,CAACK,IAAI,GAAGwB,SAAS;MACrC;MAEA,MAAMS,WAAW,GAAGC,cAAc,CAAC,IAAI,EAAEzC,IAAI,CAAC;MAI9CF,CAAC,CAAC4C,gBAAgB,CAACxC,IAAI,CAACyC,EAAE,CAAC;MAI3B,MAAMC,IAAI,GAAG,IAAAC,YAAK,EAAC7C,IAAI,CAAC;MAExB,MAAM4B,OAAO,GAAG;QACdkB,QAAQ,EAAE,KAAK;QACfC,aAAa,EAAE,KAAK;QACpBC,SAAS,EAAEA,CAAA,KAAMlD,CAAC,CAACmD,KAAK,CAAC3B,MAAM;MACjC,CAAC;MAEDtB,IAAI,CAACyB,QAAQ,CAACyB,oBAAoB,EAAEtB,OAAO,CAAC;MAE5C,IAAIA,OAAO,CAACmB,aAAa,EAAE;QACzBH,IAAI,CAACP,IAAI,CACPvC,CAAC,CAACqD,kBAAkB,CAACrD,CAAC,CAACmD,KAAK,CAAC3B,MAAM,CAAC,EAAExB,CAAC,CAACsD,UAAU,CAAC,WAAW,CAAC,CACjE,CAAC;MACH;MAEA,MAAMC,OAAO,GAAG,IAAIC,aAAO,CAACnC,SAAS,EAAEnB,IAAI,CAACoB,KAAK,EAAEwB,IAAI,EAAE,IAAI,CAAC;MAC9DS,OAAO,CAACE,OAAO,CAACvD,IAAI,CAACU,GAAG,CAAC,MAAM,CAAC,CAAC;MAEjC,IAAIkC,IAAI,CAACL,MAAM,GAAG,CAAC,EAAE;QACnBT,SAAS,CAACO,IAAI,CAACvC,CAAC,CAAC0D,mBAAmB,CAAC,KAAK,EAAEZ,IAAI,CAAC,CAAC;MACpD;MAEA,MAAMa,QAAe,GAAG,CAACJ,OAAO,CAACK,kBAAkB,CAAC,CAAC,CAAC;MACtD,MAAMC,WAAW,GAAGN,OAAO,CAACO,cAAc,CAAC,CAAC;MAE5C,IAAI1D,IAAI,CAACM,SAAS,EAAE;QAClBiD,QAAQ,CAACpB,IAAI,CAACG,WAAW,CAAC;MAC5B,CAAC,MAAM,IAAIZ,OAAO,CAACkB,QAAQ,IAAIa,WAAW,IAAIzD,IAAI,CAACO,KAAK,EAAE;QAIxDgD,QAAQ,CAACpB,IAAI,CAACvC,CAAC,CAAC+D,WAAW,CAAC,CAAC,CAAC;MAChC;MACA,IAAIjC,OAAO,CAACkB,QAAQ,EAAE;QACpBW,QAAQ,CAACpB,IAAI,CAACvC,CAAC,CAACgE,cAAc,CAAC,CAAC,CAAC;MACnC,CAAC,MAAM,IAAIH,WAAW,IAAIzD,IAAI,CAACO,KAAK,EAAE;QACpCgD,QAAQ,CAACpB,IAAI,CAACvC,CAAC,CAAC+D,WAAW,CAAC,CAAC,CAAC;MAChC;MACA,IAAIF,WAAW,EAAE;QACfF,QAAQ,CAACpB,IAAI,CAACsB,WAAW,CAAC;MAC5B,CAAC,MAAM,IAAIzD,IAAI,CAACO,KAAK,EAAE;QACrBgD,QAAQ,CAACpB,IAAI,CAACvC,CAAC,CAAC+D,WAAW,CAAC,CAAC,CAAC;MAChC;MAEA,IAAI3D,IAAI,CAACO,KAAK,EAAE;QAGd,IAAIsD,YAAY,GAAG/D,IAAI,CAACoB,KAAK;QAC7B,GAAG;UACD,IAAI2C,YAAY,CAACC,aAAa,CAAC,SAAS,CAAC,EACvCD,YAAY,CAACE,MAAM,CAAC,SAAS,CAAC;QAClC,CAAC,QAASF,YAAY,GAAGA,YAAY,CAACG,MAAM;QAE5CT,QAAQ,CAACpB,IAAI,CAACvC,CAAC,CAACsD,UAAU,CAAC,SAAS,CAAC,CAAC;MACxC;MAEA,MAAMe,QAAQ,GAAGrE,CAAC,CAACe,cAAc,CACChB,IAAI,CAACuE,mBAAmB,CAAC,IAAI,CAAC,GAC1D,CAAClE,IAAI,CAACO,KAAK,GACTX,CAAC,CAACuE,gBAAgB,CAChBvE,CAAC,CAACe,cAAc,CAAC,IAAI,CAACyD,SAAS,CAAC,aAAa,CAAC,EAAE,EAAE,CAAC,EACnDxE,CAAC,CAACsD,UAAU,CAAC,GAAG,CAClB,CAAC,GACDlD,IAAI,CAACM,SAAS,GACZ,IAAI,CAAC8D,SAAS,CAAC,qBAAqB,CAAC,GACrC,IAAI,CAACA,SAAS,CAAC,kBAAkB,CAAC,GACtCzE,IAAI,CAAC0E,eAAe,CAAC,IAAI,EAAErE,IAAI,CAACO,KAAK,GAAG,OAAO,GAAG,MAAM,CAAC,EAC7DgD,QACF,CAAC;MAED3B,SAAS,CAACO,IAAI,CAACvC,CAAC,CAACc,eAAe,CAACuD,QAAQ,CAAC,CAAC;MAC3CjE,IAAI,CAACK,IAAI,GAAGT,CAAC,CAAC0E,cAAc,CAAC1C,SAAS,CAAC;MAGvC9B,IAAI,CAACU,GAAG,CAAC,WAAW,CAAC,CAACsB,OAAO,CAAEyC,CAAM,IAAKA,CAAC,CAACrD,KAAK,CAACsD,mBAAmB,CAACD,CAAC,CAAC,CAAC;MAEzE,MAAME,aAAa,GAAGnD,aAAa,CAACtB,IAAI,CAAC0E,UAAU;MACnD,IAAID,aAAa,EAAE;QAGjBzE,IAAI,CAACK,IAAI,CAACqE,UAAU,GAAGD,aAAa;MACtC;MAEA,MAAME,oBAAoB,GAAG3E,IAAI,CAACM,SAAS;MAC3C,IAAIqE,oBAAoB,EAAE;QACxB3E,IAAI,CAACM,SAAS,GAAG,KAAK;MACxB;MAEA,IAAIN,IAAI,CAACO,KAAK,EAAE;QACdP,IAAI,CAACO,KAAK,GAAG,KAAK;MACpB;MAEA,IAAIoE,oBAAoB,IAAI/E,CAAC,CAACgF,YAAY,CAAC5E,IAAI,CAAC,EAAE;QAChDL,IAAI,CAACkF,mBAAmB,CACtB/E,IAAI,EACJF,CAAC,CAACe,cAAc,CACkBhB,IAAI,CAACuE,mBAAmB,CAAC,IAAI,CAAC,GAC1DtE,CAAC,CAACuE,gBAAgB,CAChBvE,CAAC,CAACe,cAAc,CAAC,IAAI,CAACyD,SAAS,CAAC,aAAa,CAAC,EAAE,EAAE,CAAC,EACnDxE,CAAC,CAACsD,UAAU,CAAC,GAAG,CAClB,CAAC,GACDvD,IAAI,CAAC0E,eAAe,CAAC,IAAI,EAAE,MAAM,CAAC,EACtC,CAACrE,IAAI,CACP,CACF,CAAC;QACDF,IAAI,CAACgF,UAAU,CAAC,SAAS,EAAE,WAAW,CAAC;MACzC;MAEA,MAAMC,YAAY,GAAG5B,OAAO,CAAC6B,eAAe,CAAC,CAAC;MAE9ClF,IAAI,CAACyB,QAAQ,CAAC;QACZ0D,cAAcA,CAACnF,IAAS,EAAE;UACxB,IAAI,CAACiF,YAAY,CAACG,GAAG,CAACpF,IAAI,CAACE,IAAI,CAAC,EAAE;YAChC;UACF;UAEAF,IAAI,CAACqF,WAAW,CAACvF,CAAC,CAACwF,cAAc,CAACtF,IAAI,CAACE,IAAI,CAACZ,KAAK,CAAC,CAAC;QACrD;MACF,CAAC,CAAC;MAKFU,IAAI,CAACuF,OAAO,CAAC,CAAC;IAChB,CAAC;EACH;AACF,CAAC,CAAC;AAAClG,OAAA,CAAAE,UAAA,GAAAA,UAAA;AAGH,SAASY,gBAAgBA,CAACD,IAAS,EAAED,KAAU,EAAE;EAC/C,IAAIC,IAAI,CAACM,SAAS,EAAE;IAClB,IAAIN,IAAI,CAACO,KAAK,EAAE;MAEd,OAAOR,KAAK,CAACuF,IAAI,CAACC,eAAe,KAAK,KAAK;IAC7C,CAAC,MAAM;MAEL,OAAOxF,KAAK,CAACuF,IAAI,CAACE,UAAU,KAAK,KAAK;IACxC;EACF,CAAC,MAAM,IAAIxF,IAAI,CAACO,KAAK,EAAE;IAErB,OAAOR,KAAK,CAACuF,IAAI,CAAC/E,KAAK,KAAK,KAAK;EACnC,CAAC,MAAM;IAEL,OAAO,KAAK;EACd;AACF;AAMA,SAASgC,cAAcA,CAACxC,KAAiB,EAAE0F,OAAY,EAAE;EACvD,MAAM7F,CAAC,GAAGD,IAAI,CAAC+F,QAAQ,CAAC,CAAC;EACzB,MAAM1F,IAAI,GAAGyF,OAAO,CAACzF,IAAI;EACzBJ,CAAC,CAAC+F,cAAc,CAAC3F,IAAI,CAAC;EAEtB,IAAI,CAACA,IAAI,CAACyC,EAAE,EAAE;IAGZzC,IAAI,CAACyC,EAAE,GAAGgD,OAAO,CAACvE,KAAK,CAAC8C,MAAM,CAAC7C,qBAAqB,CAAC,QAAQ,CAAC;EAChE;EAEA,IACEnB,IAAI,CAACM,SAAS,IACdV,CAAC,CAACgG,qBAAqB,CAAC5F,IAAI,CAAC,EAC7B;IAEA,OAAO6F,mBAAmB,CAAC9F,KAAK,EAAE0F,OAAO,CAAC;EAC5C;EAEA,OAAO7F,CAAC,CAACmD,KAAK,CAAC/C,IAAI,CAACyC,EAAE,CAAC;AACzB;AAEA,MAAMqD,QAAQ,GAAG,IAAIC,OAAO,CAAC,CAAC;AAE9B,SAASC,WAAWA,CAAChG,IAAS,EAAE;EAC9B,IAAI,CAAC8F,QAAQ,CAACZ,GAAG,CAAClF,IAAI,CAAC,EAAE;IACvB8F,QAAQ,CAACrF,GAAG,CAACT,IAAI,EAAE,CAAC,CAAC,CAAC;EACxB;EACA,OAAO8F,QAAQ,CAACtF,GAAG,CAACR,IAAI,CAAC;AAC3B;AAEA,SAAS6F,mBAAmBA,CAAC9F,KAAiB,EAAE0F,OAAY,EAAE;EAC5D,MAAM7F,CAAC,GAAGD,IAAI,CAAC+F,QAAQ,CAAC,CAAC;EACzB,MAAM1F,IAAI,GAAGyF,OAAO,CAACzF,IAAI;EACzBJ,CAAC,CAAC4C,gBAAgB,CAACxC,IAAI,CAACyC,EAAE,CAAC;EAE3B,MAAMwD,SAAS,GAAGR,OAAO,CAACS,UAAU,CAAC,UAAUpG,IAAS,EAAE;IACxD,OAAOA,IAAI,CAACqG,SAAS,CAAC,CAAC,IAAIrG,IAAI,CAACsG,gBAAgB,CAAC,CAAC;EACpD,CAAC,CAAC;EAEF,IAAI,CAACH,SAAS,EAAE;IACd,OAAOjG,IAAI,CAACyC,EAAE;EAChB;EAEA,MAAM4D,KAAK,GAAGJ,SAAS,CAACjG,IAAI;EAC5BsG,OAAM,CAACC,EAAE,CAACC,KAAK,CAACC,OAAO,CAACJ,KAAK,CAAChG,IAAI,CAAC,CAAC;EAEpC,MAAMqG,IAAI,GAAGV,WAAW,CAACK,KAAK,CAAC;EAC/B,IAAI,CAACK,IAAI,CAACC,IAAI,EAAE;IACdD,IAAI,CAACC,IAAI,GAAG/G,CAAC,CAAC0D,mBAAmB,CAAC,KAAK,EAAE,EAAE,CAAC;IAC5C2C,SAAS,CAACW,gBAAgB,CAAC,MAAM,EAAEF,IAAI,CAACC,IAAI,CAAC;IAC7CD,IAAI,CAACG,QAAQ,GAAGZ,SAAS,CAACzF,GAAG,CAAC,QAAQ,CAAC;EACzC;EAEA8F,OAAM,CAACQ,WAAW,CAACJ,IAAI,CAACG,QAAQ,CAAC7G,IAAI,EAAE0G,IAAI,CAACC,IAAI,CAAC;EAGjD,MAAMI,QAAQ,GAAGd,SAAS,CAAC/E,KAAK,CAACC,qBAAqB,CAAC,QAAQ,CAAC;EAChE,MAAM6F,WAAW,GAAGpH,CAAC,CAACe,cAAc,CACFhB,IAAI,CAACuE,mBAAmB,CAACnE,KAAK,CAAC,GAC3DH,CAAC,CAACuE,gBAAgB,CAChBvE,CAAC,CAACe,cAAc,CAACZ,KAAK,CAACqE,SAAS,CAAC,aAAa,CAAC,EAAE,EAAE,CAAC,EACpDxE,CAAC,CAACsD,UAAU,CAAC,GAAG,CAClB,CAAC,GACDvD,IAAI,CAAC0E,eAAe,CAACtE,KAAK,EAAE,MAAM,CAAC,EACvC,CAACH,CAAC,CAACmD,KAAK,CAAC/C,IAAI,CAACyC,EAAE,CAAC,CACnB,CAAC;EAED,MAAMwE,KAAK,GACTP,IAAI,CAACC,IAAI,CAACO,YAAY,CAAC/E,IAAI,CAACvC,CAAC,CAACqD,kBAAkB,CAAC8D,QAAQ,EAAEC,WAAW,CAAC,CAAC,GACxE,CAAC;EAEH,MAAMG,eAAe,GAAGT,IAAI,CAACG,QAAQ,CAACrG,GAAG,CAAC,eAAe,GAAGyG,KAAK,GAAG,OAAO,CAAC;EAE5EX,OAAM,CAACQ,WAAW,CAACK,eAAe,CAACnH,IAAI,EAAEgH,WAAW,CAAC;EAErDG,eAAe,CAACrC,UAAU,CAAC,SAAS,EAAE,WAAW,CAAC;EAElD,OAAOlF,CAAC,CAACmD,KAAK,CAACgE,QAAQ,CAAC;AAC1B;AAEA,MAAM/D,oBAAoB,GAAG;EAC3B,+CAA+C,EAAE,SAAAoE,CAAUtH,IAAS,EAAE;IACpEA,IAAI,CAACuH,IAAI,CAAC,CAAC;EACb,CAAC;EAEDC,UAAU,EAAE,SAAAA,CAAUxH,IAAS,EAAEC,KAAU,EAAE;IAC3C,IAAID,IAAI,CAACE,IAAI,CAACuH,IAAI,KAAK,WAAW,IAAI5H,IAAI,CAAC6H,WAAW,CAAC1H,IAAI,CAAC,EAAE;MAC5DH,IAAI,CAACkF,mBAAmB,CAAC/E,IAAI,EAAEC,KAAK,CAAC+C,SAAS,CAAC,CAAC,CAAC;MACjD/C,KAAK,CAAC8C,aAAa,GAAG,IAAI;IAC5B;EACF,CAAC;EAED4E,cAAc,EAAE,SAAAA,CAAU3H,IAAS,EAAEC,KAAU,EAAE;IAC/CA,KAAK,CAAC6C,QAAQ,GAAG,IAAI;EACvB;AACF,CAAC;AAED,MAAMnB,mBAAmB,GAAG;EAC1BiG,YAAYA,CAAC5H,IAAS,EAAEC,KAAU,EAAE;IAClC,MAAM;MAAEC;IAAK,CAAC,GAAGF,IAAI;IAErB,IAAIE,IAAI,CAAC2H,IAAI,CAACJ,IAAI,KAAK,UAAU,IAAIvH,IAAI,CAAC4H,QAAQ,CAACL,IAAI,KAAK,MAAM,EAAE;MAClE,MAAM3H,CAAC,GAAGD,IAAI,CAAC+F,QAAQ,CAAC,CAAC;MACzB/F,IAAI,CAACkF,mBAAmB,CACtB/E,IAAI,EACJF,CAAC,CAACuE,gBAAgB,CAChBvE,CAAC,CAACmD,KAAK,CAAE,IAAI,CAASrB,OAAO,CAAC,EAC9B9B,CAAC,CAACsD,UAAU,CAERvD,IAAI,CAACuE,mBAAmB,CAACnE,KAAK,CAAC4B,UAAU,CAAC,GACxC,GAAG,GACH,OACN,CACF,CACF,CAAC;IACH;EACF;AACF,CAAC;AAED,MAAMH,YAAiC,GAAG;EACxCX,QAAQ,EAAE,SAAAA,CAAUf,IAAS,EAAE;IAC7BA,IAAI,CAACuH,IAAI,CAAC,CAAC;EACb,CAAC;EAEDQ,eAAe,EAAE,SAAAA,CAAU/H,IAAS,EAAE;IACpC,MAAMF,CAAC,GAAGD,IAAI,CAAC+F,QAAQ,CAAC,CAAC;IAGzB,MAAMoC,QAAQ,GAAGhI,IAAI,CAACE,IAAI,CAAC8H,QAAQ;IAEnC,MAAMC,MAAM,GAMsBpI,IAAI,CAACuE,mBAAmB,CAAC,IAAI,CAAC,GAC1D,IAAI,CAACE,SAAS,CAAC,qBAAqB,CAAC,GACrCzE,IAAI,CAAC0E,eAAe,CAAC,IAAI,EAAE,OAAO,CAAC;IAKzC1E,IAAI,CAACkF,mBAAmB,CACtB/E,IAAI,EACJF,CAAC,CAACoI,eAAe,CAACpI,CAAC,CAACe,cAAc,CAACoH,MAAM,EAAE,CAACD,QAAQ,CAAC,CAAC,EAAE,KAAK,CAC/D,CAAC;EACH;AACF,CAAC", "ignoreList": []}