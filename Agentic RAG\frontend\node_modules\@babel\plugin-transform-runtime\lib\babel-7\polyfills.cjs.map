{"version": 3, "names": ["pluginCorejs2", "require", "default", "pluginCorejs3", "pluginRegenerator", "pluginsCompat", "createCorejs2Plugin", "options", "api", "_", "filename", "createCorejs3Plugin", "createRegeneratorPlugin", "useRuntimeRegenerator", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "undefined", "Object", "assign", "inherits", "module", "exports", "createBasePolyfillsPlugin", "corejs", "regenerator", "moduleName", "runtimeVersion", "absoluteImports", "proposals", "rawVersion", "version", "Boolean", "corejsVersion", "Number", "includes", "Error", "JSON", "stringify", "polyfillOpts", "method", "useBabelRuntime", "ext"], "sources": ["../../src/babel-7/polyfills.cjs"], "sourcesContent": ["// TODO(Babel 8) Remove this file\nif (process.env.BABEL_8_BREAKING) {\n  throw new Error(\n    \"Internal Babel error: This file should only be loaded in Babel 7\",\n  );\n}\n\nconst pluginCorejs2 = require(\"babel-plugin-polyfill-corejs2\").default;\nconst pluginCorejs3 = require(\"babel-plugin-polyfill-corejs3\").default;\nconst pluginRegenerator = require(\"babel-plugin-polyfill-regenerator\").default;\n\nconst pluginsCompat = \"#__secret_key__@babel/runtime__compatibility\";\n\nfunction createCorejs2Plugin(options) {\n  return (api, _, filename) => pluginCorejs2(api, options, filename);\n}\n\nfunction createCorejs3Plugin(options) {\n  return (api, _, filename) => pluginCorejs3(api, options, filename);\n}\n\nfunction createRegeneratorPlugin(options, useRuntimeRegenerator, corejsPlugin) {\n  if (!useRuntimeRegenerator) return corejsPlugin ?? undefined;\n  return (api, _, filename) => {\n    return {\n      ...pluginRegenerator(api, options, filename),\n      inherits: corejsPlugin ?? undefined,\n    };\n  };\n}\n\nmodule.exports = function createBasePolyfillsPlugin(\n  { corejs, regenerator = true, moduleName },\n  runtimeVersion,\n  absoluteImports,\n) {\n  let proposals = false;\n  let rawVersion;\n\n  if (typeof corejs === \"object\" && corejs !== null) {\n    rawVersion = corejs.version;\n    proposals = Boolean(corejs.proposals);\n  } else {\n    rawVersion = corejs;\n  }\n\n  const corejsVersion = rawVersion ? Number(rawVersion) : false;\n\n  if (![false, 2, 3].includes(corejsVersion)) {\n    throw new Error(\n      `The \\`core-js\\` version must be false, 2 or 3, but got ${JSON.stringify(\n        rawVersion,\n      )}.`,\n    );\n  }\n\n  if (proposals && (!corejsVersion || corejsVersion < 3)) {\n    throw new Error(\n      \"The 'proposals' option is only supported when using 'corejs: 3'\",\n    );\n  }\n\n  if (typeof regenerator !== \"boolean\") {\n    throw new Error(\n      \"The 'regenerator' option must be undefined, or a boolean.\",\n    );\n  }\n\n  const polyfillOpts = {\n    method: \"usage-pure\",\n    absoluteImports,\n    proposals,\n    [pluginsCompat]: {\n      useBabelRuntime: true,\n      runtimeVersion,\n      ext: \"\",\n      moduleName,\n    },\n  };\n\n  return createRegeneratorPlugin(\n    polyfillOpts,\n    regenerator,\n    corejsVersion === 2\n      ? createCorejs2Plugin(polyfillOpts)\n      : corejsVersion === 3\n        ? createCorejs3Plugin(polyfillOpts)\n        : null,\n  );\n};\n"], "mappings": ";AAOA,MAAMA,aAAa,GAAGC,OAAO,CAAC,+BAA+B,CAAC,CAACC,OAAO;AACtE,MAAMC,aAAa,GAAGF,OAAO,CAAC,+BAA+B,CAAC,CAACC,OAAO;AACtE,MAAME,iBAAiB,GAAGH,OAAO,CAAC,mCAAmC,CAAC,CAACC,OAAO;AAE9E,MAAMG,aAAa,GAAG,8CAA8C;AAEpE,SAASC,mBAAmBA,CAACC,OAAO,EAAE;EACpC,OAAO,CAACC,GAAG,EAAEC,CAAC,EAAEC,QAAQ,KAAKV,aAAa,CAACQ,GAAG,EAAED,OAAO,EAAEG,QAAQ,CAAC;AACpE;AAEA,SAASC,mBAAmBA,CAACJ,OAAO,EAAE;EACpC,OAAO,CAACC,GAAG,EAAEC,CAAC,EAAEC,QAAQ,KAAKP,aAAa,CAACK,GAAG,EAAED,OAAO,EAAEG,QAAQ,CAAC;AACpE;AAEA,SAASE,uBAAuBA,CAACL,OAAO,EAAEM,qBAAqB,EAAEC,YAAY,EAAE;EAC7E,IAAI,CAACD,qBAAqB,EAAE,OAAOC,YAAY,WAAZA,YAAY,GAAIC,SAAS;EAC5D,OAAO,CAACP,GAAG,EAAEC,CAAC,EAAEC,QAAQ,KAAK;IAC3B,OAAAM,MAAA,CAAAC,MAAA,KACKb,iBAAiB,CAACI,GAAG,EAAED,OAAO,EAAEG,QAAQ,CAAC;MAC5CQ,QAAQ,EAAEJ,YAAY,WAAZA,YAAY,GAAIC;IAAS;EAEvC,CAAC;AACH;AAEAI,MAAM,CAACC,OAAO,GAAG,SAASC,yBAAyBA,CACjD;EAAEC,MAAM;EAAEC,WAAW,GAAG,IAAI;EAAEC;AAAW,CAAC,EAC1CC,cAAc,EACdC,eAAe,EACf;EACA,IAAIC,SAAS,GAAG,KAAK;EACrB,IAAIC,UAAU;EAEd,IAAI,OAAON,MAAM,KAAK,QAAQ,IAAIA,MAAM,KAAK,IAAI,EAAE;IACjDM,UAAU,GAAGN,MAAM,CAACO,OAAO;IAC3BF,SAAS,GAAGG,OAAO,CAACR,MAAM,CAACK,SAAS,CAAC;EACvC,CAAC,MAAM;IACLC,UAAU,GAAGN,MAAM;EACrB;EAEA,MAAMS,aAAa,GAAGH,UAAU,GAAGI,MAAM,CAACJ,UAAU,CAAC,GAAG,KAAK;EAE7D,IAAI,CAAC,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC,CAACK,QAAQ,CAACF,aAAa,CAAC,EAAE;IAC1C,MAAM,IAAIG,KAAK,CACb,0DAA0DC,IAAI,CAACC,SAAS,CACtER,UACF,CAAC,GACH,CAAC;EACH;EAEA,IAAID,SAAS,KAAK,CAACI,aAAa,IAAIA,aAAa,GAAG,CAAC,CAAC,EAAE;IACtD,MAAM,IAAIG,KAAK,CACb,iEACF,CAAC;EACH;EAEA,IAAI,OAAOX,WAAW,KAAK,SAAS,EAAE;IACpC,MAAM,IAAIW,KAAK,CACb,2DACF,CAAC;EACH;EAEA,MAAMG,YAAY,GAAG;IACnBC,MAAM,EAAE,YAAY;IACpBZ,eAAe;IACfC,SAAS;IACT,CAACtB,aAAa,GAAG;MACfkC,eAAe,EAAE,IAAI;MACrBd,cAAc;MACde,GAAG,EAAE,EAAE;MACPhB;IACF;EACF,CAAC;EAED,OAAOZ,uBAAuB,CAC5ByB,YAAY,EACZd,WAAW,EACXQ,aAAa,KAAK,CAAC,GACfzB,mBAAmB,CAAC+B,YAAY,CAAC,GACjCN,aAAa,KAAK,CAAC,GACjBpB,mBAAmB,CAAC0B,YAAY,CAAC,GACjC,IACR,CAAC;AACH,CAAC", "ignoreList": []}