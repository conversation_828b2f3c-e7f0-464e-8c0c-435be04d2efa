# 🧪 Testing Guide

This guide provides comprehensive testing instructions for <PERSON><PERSON><PERSON>'s Agentic RAG application.

## 🚀 Quick Test

### 1. Setup and Start
```bash
# Setup (first time only)
python setup.py

# Start application
python start.py
# OR
./start.sh    # Unix
start.bat     # Windows
```

### 2. Access Application
- Open browser: http://localhost:3000
- Login: username=`karthick`, password=`1858`

### 3. Basic Test Flow
1. **Upload a test document** (PDF, image, or text file)
2. **Wait for processing** (check sidebar for document count)
3. **Ask a question** about the uploaded content
4. **Verify response** is based only on uploaded content

## 📋 Detailed Testing Scenarios

### Authentication Testing

#### ✅ Valid Login
- **Input**: username=`karthick`, password=`1858`
- **Expected**: Successful login, redirect to main page
- **Verify**: JWT token stored, authenticated state

#### ❌ Invalid Login
- **Input**: wrong username/password
- **Expected**: Error message displayed
- **Verify**: No token stored, remains on login page

#### 🔒 Protected Routes
- **Test**: Access `/app` without login
- **Expected**: Redirect to login page
- **Verify**: Authentication required for all main features

### File Upload Testing

#### 📄 PDF Upload
```bash
# Test files you can create:
echo "This is a test document about artificial intelligence." > test.txt
```

- **Upload**: PDF file
- **Expected**: Content extracted, chunks created
- **Verify**: Document appears in sidebar, chunk count increases

#### 🖼️ Image Upload (OCR)
- **Upload**: Image with text (screenshot, photo of document)
- **Expected**: OCR extraction, text chunks created
- **Verify**: Extracted text visible in processing results

#### 📝 Text File Upload
- **Upload**: .txt, .md, .py, .js files
- **Expected**: Direct text processing
- **Verify**: Content chunked and stored

#### ❌ Unsupported File Types
- **Upload**: .exe, .zip, .mp4 files
- **Expected**: Error message, file rejected
- **Verify**: No processing attempted

#### 📏 Large File Testing
- **Upload**: File > 50MB
- **Expected**: Size limit error
- **Verify**: Upload rejected before processing

### RAG System Testing

#### 🎯 Relevant Questions
```
Test Document: "The capital of France is Paris. It has a population of 2.2 million."

Good Questions:
- "What is the capital of France?"
- "What is the population of Paris?"
- "Tell me about France's capital city."

Expected: Accurate answers with source citations
```

#### 🚫 Irrelevant Questions
```
Questions about content NOT in documents:
- "What is the capital of Germany?" (if not uploaded)
- "How do I cook pasta?" (unrelated topic)

Expected: "I don't have information about that in the uploaded documents"
```

#### 📚 Multi-Document Testing
- **Upload**: Multiple documents on different topics
- **Ask**: Questions spanning multiple documents
- **Verify**: Relevant sources cited, context from multiple files

### Knowledge Persistence Testing

#### 💾 Session Persistence
1. **Upload documents** and ask questions
2. **Refresh browser** or restart application
3. **Verify**: Documents still available, can ask questions
4. **Expected**: Knowledge base persists across sessions

#### 🗄️ Database Testing
- **Location**: `data/knowledge_base.db`
- **Verify**: File exists and grows with uploads
- **Test**: Delete database, restart app, verify empty state

### Performance Testing

#### ⚡ Response Time
- **Upload**: Various file sizes
- **Measure**: Processing time displayed in chat
- **Expected**: Reasonable response times (< 30 seconds for most files)

#### 🔄 Concurrent Uploads
- **Test**: Upload multiple files simultaneously
- **Verify**: All files processed correctly
- **Expected**: No conflicts or data corruption

#### 💭 Chat Performance
- **Test**: Ask multiple questions rapidly
- **Verify**: All responses generated correctly
- **Expected**: No timeouts or errors

### Error Handling Testing

#### 🌐 Network Errors
- **Test**: Disconnect internet during LLM API call
- **Expected**: Graceful error message
- **Verify**: Application remains functional

#### 🔑 Invalid API Token
- **Test**: Set invalid HF_TOKEN in .env
- **Expected**: Clear error message about API authentication
- **Verify**: Upload works, but chat fails gracefully

#### 💥 Server Errors
- **Test**: Stop backend while frontend running
- **Expected**: Connection error messages
- **Verify**: Frontend handles backend unavailability

### UI/UX Testing

#### 📱 Responsive Design
- **Test**: Different screen sizes (mobile, tablet, desktop)
- **Verify**: Layout adapts correctly
- **Expected**: Usable on all screen sizes

#### 🎨 Dark Theme
- **Verify**: Consistent dark theme throughout
- **Test**: All components visible and readable
- **Expected**: Professional dark appearance

#### ♿ Accessibility
- **Test**: Keyboard navigation
- **Verify**: Screen reader compatibility
- **Expected**: Accessible to users with disabilities

## 🔧 API Testing

### Direct API Testing

#### Authentication
```bash
# Login
curl -X POST "http://localhost:8000/login" \
  -H "Content-Type: multipart/form-data" \
  -F "username=karthick" \
  -F "password=1858"
```

#### File Upload
```bash
# Upload file (replace TOKEN with actual JWT)
curl -X POST "http://localhost:8000/upload" \
  -H "Authorization: Bearer TOKEN" \
  -F "files=@test.txt"
```

#### Ask Question
```bash
# Ask question
curl -X POST "http://localhost:8000/ask" \
  -H "Authorization: Bearer TOKEN" \
  -F "question=What is this document about?"
```

#### Knowledge Status
```bash
# Get status
curl -X GET "http://localhost:8000/knowledge-status" \
  -H "Authorization: Bearer TOKEN"
```

## 🐛 Common Issues and Solutions

### Backend Issues

#### Import Errors
```bash
# Solution: Ensure virtual environment is activated
cd backend
source venv/bin/activate  # Unix
venv\Scripts\activate     # Windows
```

#### Tesseract Not Found
```bash
# Install Tesseract OCR
# Windows: Download from GitHub releases
# macOS: brew install tesseract
# Ubuntu: sudo apt-get install tesseract-ocr
```

#### Database Errors
```bash
# Reset database
rm data/knowledge_base.db
# Restart application
```

### Frontend Issues

#### Node Modules Missing
```bash
cd frontend
npm install
```

#### CORS Errors
- **Check**: Backend running on port 8000
- **Verify**: Frontend proxy configuration in package.json

#### Build Errors
```bash
# Clear cache and reinstall
rm -rf node_modules package-lock.json
npm install
```

### Environment Issues

#### Missing .env File
```bash
# Copy template
cp .env.example .env
# Edit with your Hugging Face token
```

#### Invalid Hugging Face Token
- **Get token**: https://huggingface.co/settings/tokens
- **Permissions**: Read access required
- **Model access**: Ensure access to Meta-Llama-3-8B-Instruct

## ✅ Test Checklist

### Pre-deployment Checklist

- [ ] All dependencies installed
- [ ] Environment variables configured
- [ ] Tesseract OCR available
- [ ] Valid Hugging Face token
- [ ] Database directory writable
- [ ] Upload directory writable

### Functional Testing

- [ ] Login/logout works
- [ ] PDF upload and processing
- [ ] Image OCR processing
- [ ] Text file processing
- [ ] Chat responses accurate
- [ ] Source citations correct
- [ ] Knowledge persistence
- [ ] Error handling graceful

### Performance Testing

- [ ] Upload processing reasonable
- [ ] Chat response times acceptable
- [ ] Memory usage stable
- [ ] No memory leaks
- [ ] Concurrent operations work

### Security Testing

- [ ] Authentication required
- [ ] JWT tokens secure
- [ ] File upload validation
- [ ] No unauthorized access
- [ ] Input sanitization

## 📊 Test Data Suggestions

### Sample Documents

1. **Technical Document**: API documentation, code comments
2. **Business Document**: Company policies, procedures
3. **Academic Paper**: Research findings, methodology
4. **Mixed Content**: Document with text, tables, images
5. **Large Document**: Multi-page PDF with various sections

### Sample Questions

1. **Factual**: "What is the main topic of this document?"
2. **Specific**: "What are the three key requirements mentioned?"
3. **Comparative**: "How does approach A differ from approach B?"
4. **Summarization**: "Can you summarize the main findings?"
5. **Cross-reference**: "Which documents mention artificial intelligence?"

## 🎯 Success Criteria

### Minimum Viable Product (MVP)
- ✅ User can login
- ✅ User can upload documents
- ✅ User can ask questions
- ✅ System provides relevant answers
- ✅ Knowledge persists across sessions

### Enhanced Features
- ✅ Multiple file type support
- ✅ Source citations
- ✅ Confidence scores
- ✅ Processing statistics
- ✅ Modern UI/UX

### Production Ready
- ✅ Error handling
- ✅ Performance optimization
- ✅ Security measures
- ✅ Documentation complete
- ✅ Testing comprehensive
